#!/usr/bin/env python3
"""
Test script for VLM validation system.
Demonstrates validation with specific scene descriptions and emotions.
"""

import os
import sys
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from validation.vlm_validator import VLMValidator
from validation.report_generator import ValidationReportGenerator


def test_single_panel_validation():
    """Test validation of a single panel with detailed descriptions."""
    
    # Find a panel with dialogue bubbles
    bubble_panel = "outputs/manga_20250601_173550/chapter_01/with_bubbles/scene_01_bubble.png"
    
    if not Path(bubble_panel).exists():
        print(f"❌ Test panel not found: {bubble_panel}")
        return
    
    print("🧪 Testing VLM validation on single panel...")
    print(f"📁 Panel: {bubble_panel}")
    
    # Initialize validator
    validator = VLMValidator()
    
    # Test with detailed scene description
    detailed_scene = """A young ninja warrior stands ready for battle in a traditional Japanese village. 
    The character wears dark clothing and carries weapons. In the background, traditional buildings 
    and rooftops are visible. The scene conveys determination and readiness for action."""
    
    # Test with emotion
    target_emotion = "determined"
    
    print(f"🎯 Scene: {detailed_scene}")
    print(f"😊 Target emotion: {target_emotion}")
    print("\n🔍 Running validation...")
    
    # Validate the panel
    result = validator.validate_panel(
        image_path=bubble_panel,
        scene_description=detailed_scene,
        target_emotion=target_emotion
    )
    
    # Print results
    print(f"\n📊 Validation Results:")
    print(f"   Overall Score: {result.overall_score:.2f}/1.0")
    print(f"   Scene Aligned: {'✅' if result.scene_aligned else '❌'} (confidence: {result.scene_confidence:.2f})")
    print(f"   Emotion Match: {'✅' if result.emotion_aligned else '❌'} (detected: {result.emotion_detected})")
    print(f"   Dialogue OK: {'✅' if result.dialogue_placement_ok else '❌'} (confidence: {result.dialogue_confidence:.2f})")
    print(f"   Issues: {', '.join(result.issues) if result.issues else 'None'}")
    
    print(f"\n💭 VLM Reasoning:")
    print(f"   Scene: {result.scene_reasoning}")
    print(f"   Emotion: {result.emotion_reasoning}")
    print(f"   Dialogue: {result.dialogue_reasoning}")
    
    return result


def test_batch_validation():
    """Test batch validation with improved scene descriptions."""
    
    print("\n🎯 Testing batch validation with improved descriptions...")
    
    # Define test panels with better descriptions
    test_panels = [
        {
            "image_path": "outputs/manga_20250601_173550/chapter_01/with_bubbles/scene_01_bubble.png",
            "scene_description": "A ninja discovers a hidden village entrance. The character stands alert with weapons ready, overlooking traditional Japanese architecture. Speech bubble shows the character's determination to find the village.",
            "emotion": "determined"
        },
        {
            "image_path": "outputs/manga_20250601_173550/chapter_01/with_bubbles/scene_02_bubble.png", 
            "scene_description": "A confrontation scene where village guards challenge the approaching ninja. Multiple characters are in combat stances with weapons drawn. Dialogue shows the guards demanding identification.",
            "emotion": "tense"
        },
        {
            "image_path": "outputs/manga_20250601_173550/chapter_01/with_bubbles/scene_03_bubble.png",
            "scene_description": "The ninja pleads for help from the village inhabitants. The scene shows the character in a more vulnerable position, asking for assistance. Speech bubble contains a request for aid.",
            "emotion": "desperate"
        }
    ]
    
    # Filter to only existing panels
    existing_panels = []
    for panel in test_panels:
        if Path(panel["image_path"]).exists():
            existing_panels.append(panel)
        else:
            print(f"⚠️  Panel not found: {panel['image_path']}")
    
    if not existing_panels:
        print("❌ No test panels found")
        return
    
    print(f"📁 Testing {len(existing_panels)} panels...")
    
    # Initialize validator
    validator = VLMValidator()
    
    # Run batch validation
    results = validator.validate_manga_batch(existing_panels)
    
    # Generate test report
    report_generator = ValidationReportGenerator()
    
    output_dir = Path("outputs/validation_test")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Save results
    json_path = output_dir / "test_results.json"
    md_path = output_dir / "test_report.md"
    html_path = output_dir / "test_report.html"
    
    report_generator.save_json_results(results, str(json_path))
    report_generator.generate_markdown_report(results, str(md_path), "VLM Validation Test Report")
    report_generator.generate_html_report(results, str(html_path), "VLM Validation Test Report")
    
    # Print summary
    total_panels = len(results)
    issues_count = sum(1 for r in results if r.issues)
    avg_score = sum(r.overall_score for r in results) / total_panels if total_panels > 0 else 0
    
    print(f"\n✅ Batch validation complete!")
    print(f"📊 Summary:")
    print(f"   Total panels: {total_panels}")
    print(f"   Panels with issues: {issues_count}")
    print(f"   Average score: {avg_score:.2f}/1.0")
    print(f"📁 Test reports saved to: {output_dir}")
    
    return results


def main():
    """Main test function."""
    print("🧪 VLM Validation System Test")
    print("=" * 50)
    
    try:
        # Test single panel validation
        single_result = test_single_panel_validation()
        
        # Test batch validation
        batch_results = test_batch_validation()
        
        print(f"\n🎉 All tests completed successfully!")
        print(f"📄 Check the generated reports for detailed analysis.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
