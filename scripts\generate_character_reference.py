#!/usr/bin/env python3
"""
Character Reference Generator

Generates reference images for main characters to ensure consistency across panels.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from scripts.generate_from_prompt import generate_manga_panel


def generate_sora_reference():
    """Generate reference image for Sora Hikari character."""
    
    # Clean, focused prompt for <PERSON><PERSON>'s reference
    sora_prompt = "full-body reference of <PERSON><PERSON>, 14-year-old girl, fiery red hair under frayed hoodie, amber eyes, worn jeans, neutral standing pose, character sheet style"
    
    print("🎨 Generating Sora Hikari character reference...")
    
    # Generate with specific style for character reference
    result_path = generate_manga_panel(
        text_prompt=sora_prompt,
        style_override="shoujo",  # Good for character details
        pose_override="standing",
        seed=12345  # Fixed seed for consistency
    )
    
    # Move to character assets folder
    reference_path = Path("assets/characters/sora_reference.png")
    reference_path.parent.mkdir(parents=True, exist_ok=True)
    
    if Path(result_path).exists():
        Path(result_path).rename(reference_path)
        print(f"✅ Sora reference saved to: {reference_path}")
        return str(reference_path)
    else:
        print("❌ Failed to generate Sora reference")
        return None


def main():
    """Generate all character references."""
    print("Character Reference Generator")
    print("=" * 40)
    
    # Generate Sora reference
    sora_ref = generate_sora_reference()
    
    if sora_ref:
        print(f"\n✅ Character references generated successfully!")
        print(f"📁 Sora: {sora_ref}")
    else:
        print(f"\n❌ Failed to generate character references")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
