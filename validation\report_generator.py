"""
Validation Report Generator

Creates comprehensive HTML and Markdown reports from VLM validation results.
"""

import json
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime
from validation.vlm_validator import ValidationResult


class ValidationReportGenerator:
    """
    Generates validation reports in multiple formats.
    """
    
    def __init__(self):
        """Initialize the report generator."""
        pass
    
    def generate_markdown_report(self, 
                                results: List[ValidationResult], 
                                output_path: str,
                                manga_title: str = "Manga Validation Report") -> str:
        """
        Generate a Markdown validation report.
        
        Args:
            results: List of validation results
            output_path: Path to save the report
            manga_title: Title for the report
            
        Returns:
            Path to generated report
        """
        # Calculate summary statistics
        total_panels = len(results)
        scene_aligned = sum(1 for r in results if r.scene_aligned)
        emotion_aligned = sum(1 for r in results if r.emotion_aligned)
        dialogue_ok = sum(1 for r in results if r.dialogue_placement_ok)
        avg_score = sum(r.overall_score for r in results) / total_panels if total_panels > 0 else 0
        
        # Count issues
        all_issues = []
        for result in results:
            all_issues.extend(result.issues)
        issue_counts = {}
        for issue in all_issues:
            issue_counts[issue] = issue_counts.get(issue, 0) + 1
        
        # Generate report content
        report_content = f"""# {manga_title}

**Generated:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 📊 Summary Statistics

- **Total Panels:** {total_panels}
- **Average Score:** {avg_score:.2f}/1.0
- **Scene Alignment:** {scene_aligned}/{total_panels} ({scene_aligned/total_panels*100:.1f}%)
- **Emotion Consistency:** {emotion_aligned}/{total_panels} ({emotion_aligned/total_panels*100:.1f}%)
- **Dialogue Placement:** {dialogue_ok}/{total_panels} ({dialogue_ok/total_panels*100:.1f}%)

## ⚠️ Issues Summary

"""
        
        if issue_counts:
            for issue, count in sorted(issue_counts.items()):
                report_content += f"- **{issue.replace('_', ' ').title()}:** {count} panels\n"
        else:
            report_content += "✅ No issues detected!\n"
        
        report_content += "\n## 📋 Panel-by-Panel Results\n\n"
        
        # Add individual panel results
        for i, result in enumerate(results, 1):
            panel_name = Path(result.panel_path).name
            
            # Status indicators
            scene_status = "✅" if result.scene_aligned else "❌"
            emotion_status = "✅" if result.emotion_aligned else ("⚠️" if result.emotion_target else "➖")
            dialogue_status = "✅" if result.dialogue_placement_ok else "❌"
            
            report_content += f"""### Panel {i}: {panel_name}

**Overall Score:** {result.overall_score:.2f}/1.0

| Aspect | Status | Confidence | Details |
|--------|--------|------------|---------|
| Scene Alignment | {scene_status} | {result.scene_confidence:.2f} | {result.scene_reasoning} |
| Emotion Consistency | {emotion_status} | {result.emotion_confidence:.2f} | Target: {result.emotion_target or 'None'}, Detected: {result.emotion_detected} - {result.emotion_reasoning} |
| Dialogue Placement | {dialogue_status} | {result.dialogue_confidence:.2f} | {result.dialogue_reasoning} |

**Scene Description:** {result.scene_description}

**Issues:** {', '.join(result.issues) if result.issues else 'None'}

---

"""
        
        # Save report
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"📄 Markdown report saved: {output_path}")
        return str(output_path)
    
    def generate_html_report(self, 
                           results: List[ValidationResult], 
                           output_path: str,
                           manga_title: str = "Manga Validation Report") -> str:
        """
        Generate an HTML validation report with embedded images.
        
        Args:
            results: List of validation results
            output_path: Path to save the report
            manga_title: Title for the report
            
        Returns:
            Path to generated report
        """
        # Calculate summary statistics
        total_panels = len(results)
        scene_aligned = sum(1 for r in results if r.scene_aligned)
        emotion_aligned = sum(1 for r in results if r.emotion_aligned)
        dialogue_ok = sum(1 for r in results if r.dialogue_placement_ok)
        avg_score = sum(r.overall_score for r in results) / total_panels if total_panels > 0 else 0
        
        # HTML template
        html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{manga_title}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }}
        .summary {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }}
        .stat-card {{ background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; border-left: 4px solid #007bff; }}
        .stat-value {{ font-size: 2em; font-weight: bold; color: #007bff; }}
        .stat-label {{ color: #666; margin-top: 5px; }}
        .panel-result {{ border: 1px solid #ddd; margin-bottom: 20px; border-radius: 8px; overflow: hidden; }}
        .panel-header {{ background: #f8f9fa; padding: 15px; border-bottom: 1px solid #ddd; }}
        .panel-content {{ padding: 20px; }}
        .panel-image {{ max-width: 300px; height: auto; border-radius: 4px; margin-bottom: 15px; }}
        .validation-table {{ width: 100%; border-collapse: collapse; margin-bottom: 15px; }}
        .validation-table th, .validation-table td {{ padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }}
        .validation-table th {{ background-color: #f8f9fa; }}
        .status-pass {{ color: #28a745; font-weight: bold; }}
        .status-fail {{ color: #dc3545; font-weight: bold; }}
        .status-warn {{ color: #ffc107; font-weight: bold; }}
        .status-na {{ color: #6c757d; }}
        .issues {{ background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 4px; margin-top: 10px; }}
        .no-issues {{ background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; border-radius: 4px; margin-top: 10px; color: #155724; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{manga_title}</h1>
            <p>Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
        </div>
        
        <div class="summary">
            <div class="stat-card">
                <div class="stat-value">{total_panels}</div>
                <div class="stat-label">Total Panels</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{avg_score:.2f}</div>
                <div class="stat-label">Average Score</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{scene_aligned}/{total_panels}</div>
                <div class="stat-label">Scene Aligned</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{emotion_aligned}/{total_panels}</div>
                <div class="stat-label">Emotion Consistent</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{dialogue_ok}/{total_panels}</div>
                <div class="stat-label">Dialogue OK</div>
            </div>
        </div>
"""
        
        # Add individual panel results
        for i, result in enumerate(results, 1):
            panel_name = Path(result.panel_path).name
            
            # Status classes
            scene_class = "status-pass" if result.scene_aligned else "status-fail"
            emotion_class = ("status-pass" if result.emotion_aligned else 
                           "status-fail" if result.emotion_target else "status-na")
            dialogue_class = "status-pass" if result.dialogue_placement_ok else "status-fail"
            
            # Convert image path to relative path for HTML
            try:
                image_path = Path(result.panel_path).as_posix()
            except:
                image_path = result.panel_path
            
            html_content += f"""
        <div class="panel-result">
            <div class="panel-header">
                <h3>Panel {i}: {panel_name}</h3>
                <strong>Overall Score: {result.overall_score:.2f}/1.0</strong>
            </div>
            <div class="panel-content">
                <img src="{image_path}" alt="Panel {i}" class="panel-image">
                
                <table class="validation-table">
                    <tr>
                        <th>Aspect</th>
                        <th>Status</th>
                        <th>Confidence</th>
                        <th>Details</th>
                    </tr>
                    <tr>
                        <td>Scene Alignment</td>
                        <td class="{scene_class}">{"✅ Pass" if result.scene_aligned else "❌ Fail"}</td>
                        <td>{result.scene_confidence:.2f}</td>
                        <td>{result.scene_reasoning}</td>
                    </tr>
                    <tr>
                        <td>Emotion Consistency</td>
                        <td class="{emotion_class}">{"✅ Pass" if result.emotion_aligned else ("❌ Fail" if result.emotion_target else "➖ N/A")}</td>
                        <td>{result.emotion_confidence:.2f}</td>
                        <td>Target: {result.emotion_target or 'None'}, Detected: {result.emotion_detected}</td>
                    </tr>
                    <tr>
                        <td>Dialogue Placement</td>
                        <td class="{dialogue_class}">{"✅ Pass" if result.dialogue_placement_ok else "❌ Fail"}</td>
                        <td>{result.dialogue_confidence:.2f}</td>
                        <td>{result.dialogue_reasoning}</td>
                    </tr>
                </table>
                
                <p><strong>Scene Description:</strong> {result.scene_description}</p>
                
                {"<div class='issues'><strong>Issues:</strong> " + ', '.join(result.issues) + "</div>" if result.issues else "<div class='no-issues'>✅ No issues detected</div>"}
            </div>
        </div>
"""
        
        html_content += """
    </div>
</body>
</html>"""
        
        # Save report
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"🌐 HTML report saved: {output_path}")
        return str(output_path)
    
    def save_json_results(self, 
                         results: List[ValidationResult], 
                         output_path: str) -> str:
        """
        Save validation results as JSON for programmatic access.
        
        Args:
            results: List of validation results
            output_path: Path to save JSON file
            
        Returns:
            Path to saved JSON file
        """
        # Convert results to serializable format
        json_data = {
            "validation_timestamp": datetime.now().isoformat(),
            "total_panels": len(results),
            "results": []
        }
        
        for result in results:
            json_data["results"].append({
                "panel_path": result.panel_path,
                "scene_description": result.scene_description,
                "scene_aligned": result.scene_aligned,
                "scene_confidence": result.scene_confidence,
                "scene_reasoning": result.scene_reasoning,
                "emotion_target": result.emotion_target,
                "emotion_detected": result.emotion_detected,
                "emotion_aligned": result.emotion_aligned,
                "emotion_confidence": result.emotion_confidence,
                "emotion_reasoning": result.emotion_reasoning,
                "dialogue_placement_ok": result.dialogue_placement_ok,
                "dialogue_confidence": result.dialogue_confidence,
                "dialogue_reasoning": result.dialogue_reasoning,
                "overall_score": result.overall_score,
                "issues": result.issues,
                "timestamp": result.timestamp
            })
        
        # Save JSON
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)
        
        print(f"💾 JSON results saved: {output_path}")
        return str(output_path)
