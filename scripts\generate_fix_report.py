#!/usr/bin/env python3
"""
Generate Fix Delta Report

Compares validation results before and after auto-fix to show improvements.
"""

import os
import sys
import json
import argparse
from pathlib import Path
from typing import Dict, List, Any

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from auto_fix.feedback_loop import <PERSON><PERSON><PERSON><PERSON><PERSON>


def generate_fix_report(old_validation_dir: str, 
                       new_validation_dir: str,
                       output_path: str = None) -> str:
    """
    Generate a delta report comparing validation results before and after fixes.
    
    Args:
        old_validation_dir: Directory with original validation results
        new_validation_dir: Directory with fixed validation results
        output_path: Output path for the report (optional)
        
    Returns:
        Path to generated report
    """
    old_path = Path(old_validation_dir)
    new_path = Path(new_validation_dir)
    
    # Find validation results files
    old_results_file = old_path / "validation_results.json"
    new_results_file = new_path / "validation_results.json"
    
    if not old_results_file.exists():
        raise FileNotFoundError(f"Original validation results not found: {old_results_file}")
    
    if not new_results_file.exists():
        raise FileNotFoundError(f"Fixed validation results not found: {new_results_file}")
    
    print(f"📊 Comparing validation results...")
    print(f"   Original: {old_results_file}")
    print(f"   Fixed: {new_results_file}")
    
    # Load validation data
    with open(old_results_file, 'r', encoding='utf-8') as f:
        old_data = json.load(f)
    
    with open(new_results_file, 'r', encoding='utf-8') as f:
        new_data = json.load(f)
    
    # Generate comparison report
    report_content = _generate_comparison_report(old_data, new_data)
    
    # Determine output path
    if output_path is None:
        output_path = new_path / "fix_delta_report.md"
    else:
        output_path = Path(output_path)
    
    # Save report
    output_path.parent.mkdir(parents=True, exist_ok=True)
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"📄 Fix delta report generated: {output_path}")
    return str(output_path)


def _generate_comparison_report(old_data: Dict[str, Any], new_data: Dict[str, Any]) -> str:
    """
    Generate comparison report content.
    
    Args:
        old_data: Original validation data
        new_data: Fixed validation data
        
    Returns:
        Report content as markdown
    """
    from datetime import datetime
    
    # Calculate summary statistics
    old_results = old_data["results"]
    new_results = new_data["results"]
    
    old_total = len(old_results)
    new_total = len(new_results)
    
    # Calculate averages
    old_avg_score = sum(r["overall_score"] for r in old_results) / old_total if old_total > 0 else 0
    new_avg_score = sum(r["overall_score"] for r in new_results) / new_total if new_total > 0 else 0
    
    # Count issues
    old_scene_aligned = sum(1 for r in old_results if r["scene_aligned"])
    new_scene_aligned = sum(1 for r in new_results if r["scene_aligned"])
    
    old_emotion_aligned = sum(1 for r in old_results if r["emotion_aligned"])
    new_emotion_aligned = sum(1 for r in new_results if r["emotion_aligned"])
    
    old_dialogue_ok = sum(1 for r in old_results if r["dialogue_placement_ok"])
    new_dialogue_ok = sum(1 for r in new_results if r["dialogue_placement_ok"])
    
    # Count total issues
    old_total_issues = sum(len(r["issues"]) for r in old_results)
    new_total_issues = sum(len(r["issues"]) for r in new_results)
    
    # Generate report
    report_content = f"""# Fix Delta Report

**Generated:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 📊 Summary Comparison

| Metric | Before | After | Change | Status |
|--------|--------|-------|--------|--------|
| **Average Score** | {old_avg_score:.3f} | {new_avg_score:.3f} | {new_avg_score - old_avg_score:+.3f} | {"✅ Improved" if new_avg_score > old_avg_score else "❌ Declined" if new_avg_score < old_avg_score else "➖ No Change"} |
| **Scene Alignment** | {old_scene_aligned}/{old_total} ({old_scene_aligned/old_total*100:.1f}%) | {new_scene_aligned}/{new_total} ({new_scene_aligned/new_total*100:.1f}%) | {new_scene_aligned - old_scene_aligned:+d} | {"✅ Improved" if new_scene_aligned > old_scene_aligned else "❌ Declined" if new_scene_aligned < old_scene_aligned else "➖ No Change"} |
| **Emotion Consistency** | {old_emotion_aligned}/{old_total} ({old_emotion_aligned/old_total*100:.1f}%) | {new_emotion_aligned}/{new_total} ({new_emotion_aligned/new_total*100:.1f}%) | {new_emotion_aligned - old_emotion_aligned:+d} | {"✅ Improved" if new_emotion_aligned > old_emotion_aligned else "❌ Declined" if new_emotion_aligned < old_emotion_aligned else "➖ No Change"} |
| **Dialogue Placement** | {old_dialogue_ok}/{old_total} ({old_dialogue_ok/old_total*100:.1f}%) | {new_dialogue_ok}/{new_total} ({new_dialogue_ok/new_total*100:.1f}%) | {new_dialogue_ok - old_dialogue_ok:+d} | {"✅ Improved" if new_dialogue_ok > old_dialogue_ok else "❌ Declined" if new_dialogue_ok < old_dialogue_ok else "➖ No Change"} |
| **Total Issues** | {old_total_issues} | {new_total_issues} | {new_total_issues - old_total_issues:+d} | {"✅ Reduced" if new_total_issues < old_total_issues else "❌ Increased" if new_total_issues > old_total_issues else "➖ No Change"} |

## 🎯 Overall Assessment

"""
    
    # Overall assessment
    improvements = 0
    if new_avg_score > old_avg_score:
        improvements += 1
    if new_scene_aligned > old_scene_aligned:
        improvements += 1
    if new_emotion_aligned > old_emotion_aligned:
        improvements += 1
    if new_dialogue_ok > old_dialogue_ok:
        improvements += 1
    if new_total_issues < old_total_issues:
        improvements += 1
    
    if improvements >= 4:
        report_content += "🎉 **Excellent improvement** - Multiple aspects were significantly enhanced!\n\n"
    elif improvements >= 3:
        report_content += "✅ **Good improvement** - Several aspects were enhanced.\n\n"
    elif improvements >= 2:
        report_content += "🟡 **Moderate improvement** - Some aspects were enhanced.\n\n"
    elif improvements >= 1:
        report_content += "🟠 **Minor improvement** - Limited enhancement achieved.\n\n"
    else:
        report_content += "❌ **No improvement** - Fixes did not improve validation scores.\n\n"
    
    # Panel-by-panel comparison
    report_content += "## 📋 Panel-by-Panel Comparison\n\n"
    
    # Create lookup for new results
    new_lookup = {}
    for result in new_results:
        panel_name = Path(result["panel_path"]).name
        new_lookup[panel_name] = result
    
    for i, old_result in enumerate(old_results, 1):
        old_panel_name = Path(old_result["panel_path"]).name
        new_result = new_lookup.get(old_panel_name)
        
        if new_result:
            score_change = new_result["overall_score"] - old_result["overall_score"]
            status = "✅" if score_change > 0 else "❌" if score_change < 0 else "➖"
            
            # Issue comparison
            old_issues = set(old_result["issues"])
            new_issues = set(new_result["issues"])
            resolved_issues = old_issues - new_issues
            new_issues_added = new_issues - old_issues
            
            report_content += f"""### Panel {i}: {old_panel_name} {status}

**Score:** {old_result["overall_score"]:.3f} → {new_result["overall_score"]:.3f} ({score_change:+.3f})

| Aspect | Before | After | Change |
|--------|--------|-------|--------|
| Scene | {"✅" if old_result["scene_aligned"] else "❌"} ({old_result["scene_confidence"]:.2f}) | {"✅" if new_result["scene_aligned"] else "❌"} ({new_result["scene_confidence"]:.2f}) | {new_result["scene_confidence"] - old_result["scene_confidence"]:+.3f} |
| Emotion | {"✅" if old_result["emotion_aligned"] else "❌"} ({old_result["emotion_confidence"]:.2f}) | {"✅" if new_result["emotion_aligned"] else "❌"} ({new_result["emotion_confidence"]:.2f}) | {new_result["emotion_confidence"] - old_result["emotion_confidence"]:+.3f} |
| Dialogue | {"✅" if old_result["dialogue_placement_ok"] else "❌"} ({old_result["dialogue_confidence"]:.2f}) | {"✅" if new_result["dialogue_placement_ok"] else "❌"} ({new_result["dialogue_confidence"]:.2f}) | {new_result["dialogue_confidence"] - old_result["dialogue_confidence"]:+.3f} |

**Issues Resolved:** {', '.join(resolved_issues) if resolved_issues else 'None'}

**New Issues:** {', '.join(new_issues_added) if new_issues_added else 'None'}

---

"""
        else:
            report_content += f"""### Panel {i}: {old_panel_name} ❓

**Status:** Panel not found in fixed results

---

"""
    
    # Add improvement recommendations
    report_content += """## 💡 Improvement Recommendations

Based on the comparison results:

"""
    
    if new_avg_score <= old_avg_score:
        report_content += "- **Overall scores did not improve** - Consider reviewing fix strategies\n"
    
    if new_scene_aligned <= old_scene_aligned:
        report_content += "- **Scene alignment needs work** - Enhance scene descriptions or regeneration prompts\n"
    
    if new_dialogue_ok <= old_dialogue_ok:
        report_content += "- **Dialogue placement still problematic** - Improve bubble positioning algorithms\n"
    
    if new_total_issues >= old_total_issues:
        report_content += "- **Issue count not reduced** - Review fix effectiveness and validation criteria\n"
    
    if improvements >= 3:
        report_content += "- **Good progress made** - Continue with similar fix strategies\n"
        report_content += "- **Focus on remaining issues** - Target panels that still have problems\n"
    
    return report_content


def main():
    """Main entry point for the fix report generator."""
    parser = argparse.ArgumentParser(
        description="Generate delta report comparing validation results before and after fixes",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Compare validation results from two directories
  python scripts/generate_fix_report.py --old reports/validation_20250601 --new reports/fixed_20250601
  
  # Generate report with custom output path
  python scripts/generate_fix_report.py --old original/validation --new fixed/validation --output fix_comparison.md
        """
    )
    
    parser.add_argument(
        "--old",
        required=True,
        help="Directory containing original validation results"
    )
    parser.add_argument(
        "--new", 
        required=True,
        help="Directory containing fixed validation results"
    )
    parser.add_argument(
        "--output", "-o",
        help="Output path for the delta report (default: new_dir/fix_delta_report.md)"
    )
    
    args = parser.parse_args()
    
    try:
        report_path = generate_fix_report(
            old_validation_dir=args.old,
            new_validation_dir=args.new,
            output_path=args.output
        )
        
        print(f"\n✅ Fix delta report generated successfully!")
        print(f"📄 Report: {report_path}")
        
    except Exception as e:
        print(f"❌ Report generation failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
