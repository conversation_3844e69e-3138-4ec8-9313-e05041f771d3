"""
VLM-Based Manga Panel Validation System

Uses Vision-Language Models to validate manga panels for:
- Scene-content alignment
- Emotion consistency 
- Dialogue bubble placement
- Overall visual quality
"""

import os
import json
import base64
import requests
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass
from PIL import Image
import io

# Load environment variables
from dotenv import load_dotenv
load_dotenv()


@dataclass
class ValidationResult:
    """Results from VLM validation of a manga panel."""
    panel_path: str
    scene_description: str
    scene_aligned: bool
    scene_confidence: float
    scene_reasoning: str
    emotion_target: Optional[str]
    emotion_detected: Optional[str]
    emotion_aligned: bool
    emotion_confidence: float
    emotion_reasoning: str
    dialogue_placement_ok: bool
    dialogue_confidence: float
    dialogue_reasoning: str
    overall_score: float
    issues: List[str]
    timestamp: str


class VLMValidator:
    """
    Vision-Language Model validator for manga panels.
    Uses OpenRouter VLM API for visual analysis.
    """
    
    def __init__(self, 
                 primary_model: str = "qwen/qwen2.5-vl-72b-instruct:free",
                 fallback_key: str = None):
        """
        Initialize the VLM validator.
        
        Args:
            primary_model: Primary VLM model to use
            fallback_key: Fallback OpenRouter API key if primary fails
        """
        self.primary_model = primary_model
        self.primary_key = os.getenv("OPENROUTER_API_KEY")
        self.fallback_key = fallback_key or "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"
        self.base_url = "https://openrouter.ai/api/v1/chat/completions"
        
        if not self.primary_key:
            raise ValueError("OPENROUTER_API_KEY not found in environment variables")
    
    def _encode_image(self, image_path: str) -> str:
        """
        Encode image to base64 for API transmission.
        
        Args:
            image_path: Path to image file
            
        Returns:
            Base64 encoded image string
        """
        try:
            with open(image_path, "rb") as image_file:
                # Resize image if too large (VLM APIs have size limits)
                img = Image.open(image_file)
                
                # Resize if larger than 1024px on any side
                max_size = 1024
                if img.width > max_size or img.height > max_size:
                    img.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)
                
                # Convert to bytes
                buffer = io.BytesIO()
                img.save(buffer, format="PNG")
                buffer.seek(0)
                
                return base64.b64encode(buffer.read()).decode('utf-8')
                
        except Exception as e:
            raise Exception(f"Error encoding image {image_path}: {e}")
    
    def _call_vlm_api(self, 
                      messages: List[Dict[str, Any]], 
                      use_fallback: bool = False) -> Dict[str, Any]:
        """
        Call the VLM API with image and text inputs.
        
        Args:
            messages: List of message objects with text and image content
            use_fallback: Whether to use fallback API key
            
        Returns:
            API response data
        """
        api_key = self.fallback_key if use_fallback else self.primary_key
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.primary_model,
            "messages": messages,
            "max_tokens": 1000,
            "temperature": 0.3  # Lower temperature for more consistent analysis
        }
        
        try:
            response = requests.post(self.base_url, json=payload, headers=headers, timeout=60)
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            if not use_fallback and "429" in str(e):
                print("⚠️  Primary API key rate limited, trying fallback...")
                return self._call_vlm_api(messages, use_fallback=True)
            raise Exception(f"VLM API error: {e}")
    
    def validate_scene_alignment(self, 
                                image_path: str, 
                                scene_description: str) -> Tuple[bool, float, str]:
        """
        Validate if image matches the scene description.
        
        Args:
            image_path: Path to manga panel image
            scene_description: Expected scene description
            
        Returns:
            Tuple of (aligned, confidence, reasoning)
        """
        try:
            image_b64 = self._encode_image(image_path)
            
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": f"""Analyze this manga panel image and determine if it visually matches this scene description:

SCENE: "{scene_description}"

Please evaluate:
1. Does the image show the described setting/location?
2. Are the described characters/objects present?
3. Does the action/situation match the description?

Respond in this exact format:
ALIGNED: [YES/NO]
CONFIDENCE: [0.0-1.0]
REASONING: [Brief explanation of match/mismatch]"""
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{image_b64}"
                            }
                        }
                    ]
                }
            ]
            
            response = self._call_vlm_api(messages)
            content = response["choices"][0]["message"]["content"]
            
            # Parse response
            aligned = "YES" in content.split("ALIGNED:")[1].split("\n")[0].upper()
            
            try:
                confidence_line = content.split("CONFIDENCE:")[1].split("\n")[0].strip()
                confidence = float(confidence_line)
            except:
                confidence = 0.5  # Default if parsing fails
            
            try:
                reasoning = content.split("REASONING:")[1].strip()
            except:
                reasoning = "Unable to parse reasoning"
            
            return aligned, confidence, reasoning
            
        except Exception as e:
            print(f"Error validating scene alignment: {e}")
            return False, 0.0, f"Validation error: {e}"

    def validate_emotion_consistency(self,
                                   image_path: str,
                                   target_emotion: str) -> Tuple[str, bool, float, str]:
        """
        Validate if character emotions match the target emotion.

        Args:
            image_path: Path to manga panel image
            target_emotion: Expected emotion (e.g., "sad", "excited", "angry")

        Returns:
            Tuple of (detected_emotion, aligned, confidence, reasoning)
        """
        if not target_emotion:
            return "none", True, 1.0, "No target emotion specified"

        try:
            image_b64 = self._encode_image(image_path)

            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": f"""Analyze the character emotions in this manga panel.

TARGET EMOTION: "{target_emotion}"

Please evaluate:
1. What emotion(s) do you see in the character(s)?
2. Does this match the target emotion?
3. How confident are you in this assessment?

Respond in this exact format:
DETECTED: [emotion name]
ALIGNED: [YES/NO]
CONFIDENCE: [0.0-1.0]
REASONING: [Brief explanation]"""
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{image_b64}"
                            }
                        }
                    ]
                }
            ]

            response = self._call_vlm_api(messages)
            content = response["choices"][0]["message"]["content"]

            # Parse response
            try:
                detected = content.split("DETECTED:")[1].split("\n")[0].strip()
            except:
                detected = "unknown"

            aligned = "YES" in content.split("ALIGNED:")[1].split("\n")[0].upper()

            try:
                confidence_line = content.split("CONFIDENCE:")[1].split("\n")[0].strip()
                confidence = float(confidence_line)
            except:
                confidence = 0.5

            try:
                reasoning = content.split("REASONING:")[1].strip()
            except:
                reasoning = "Unable to parse reasoning"

            return detected, aligned, confidence, reasoning

        except Exception as e:
            print(f"Error validating emotion consistency: {e}")
            return "error", False, 0.0, f"Validation error: {e}"

    def validate_dialogue_placement(self, image_path: str) -> Tuple[bool, float, str]:
        """
        Validate dialogue bubble placement and alignment.

        Args:
            image_path: Path to manga panel image

        Returns:
            Tuple of (placement_ok, confidence, reasoning)
        """
        try:
            image_b64 = self._encode_image(image_path)

            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": """Analyze the dialogue bubbles in this manga panel.

Please evaluate:
1. Are speech bubbles properly positioned near speaking characters?
2. Do bubble tails point toward the correct speakers?
3. Are bubbles readable and not covering important visual elements?
4. Is the text clear and appropriately sized?

Respond in this exact format:
PLACEMENT_OK: [YES/NO]
CONFIDENCE: [0.0-1.0]
REASONING: [Brief explanation of bubble placement quality]"""
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{image_b64}"
                            }
                        }
                    ]
                }
            ]

            response = self._call_vlm_api(messages)
            content = response["choices"][0]["message"]["content"]

            # Parse response
            placement_ok = "YES" in content.split("PLACEMENT_OK:")[1].split("\n")[0].upper()

            try:
                confidence_line = content.split("CONFIDENCE:")[1].split("\n")[0].strip()
                confidence = float(confidence_line)
            except:
                confidence = 0.5

            try:
                reasoning = content.split("REASONING:")[1].strip()
            except:
                reasoning = "Unable to parse reasoning"

            return placement_ok, confidence, reasoning

        except Exception as e:
            print(f"Error validating dialogue placement: {e}")
            return False, 0.0, f"Validation error: {e}"

    def validate_panel(self,
                      image_path: str,
                      scene_description: str,
                      target_emotion: str = None) -> ValidationResult:
        """
        Perform complete validation of a manga panel.

        Args:
            image_path: Path to manga panel image
            scene_description: Expected scene description
            target_emotion: Expected emotion (optional)

        Returns:
            ValidationResult object with all validation data
        """
        print(f"🔍 Validating panel: {Path(image_path).name}")

        # Scene alignment validation
        scene_aligned, scene_confidence, scene_reasoning = self.validate_scene_alignment(
            image_path, scene_description
        )

        # Emotion consistency validation
        emotion_detected, emotion_aligned, emotion_confidence, emotion_reasoning = self.validate_emotion_consistency(
            image_path, target_emotion
        )

        # Dialogue placement validation
        dialogue_ok, dialogue_confidence, dialogue_reasoning = self.validate_dialogue_placement(
            image_path
        )

        # Calculate overall score
        scores = [scene_confidence, emotion_confidence, dialogue_confidence]
        overall_score = sum(scores) / len(scores)

        # Identify issues
        issues = []
        if not scene_aligned:
            issues.append("scene_mismatch")
        if target_emotion and not emotion_aligned:
            issues.append("emotion_mismatch")
        if not dialogue_ok:
            issues.append("dialogue_placement")

        return ValidationResult(
            panel_path=image_path,
            scene_description=scene_description,
            scene_aligned=scene_aligned,
            scene_confidence=scene_confidence,
            scene_reasoning=scene_reasoning,
            emotion_target=target_emotion,
            emotion_detected=emotion_detected,
            emotion_aligned=emotion_aligned,
            emotion_confidence=emotion_confidence,
            emotion_reasoning=emotion_reasoning,
            dialogue_placement_ok=dialogue_ok,
            dialogue_confidence=dialogue_confidence,
            dialogue_reasoning=dialogue_reasoning,
            overall_score=overall_score,
            issues=issues,
            timestamp=datetime.now().isoformat()
        )

    def validate_manga_batch(self,
                           panels_data: List[Dict[str, Any]]) -> List[ValidationResult]:
        """
        Validate multiple manga panels in batch.

        Args:
            panels_data: List of dicts with 'image_path', 'scene_description', 'emotion' keys

        Returns:
            List of ValidationResult objects
        """
        results = []

        print(f"🎯 Starting batch validation of {len(panels_data)} panels...")

        for i, panel_data in enumerate(panels_data, 1):
            print(f"📊 Progress: {i}/{len(panels_data)}")

            try:
                result = self.validate_panel(
                    image_path=panel_data["image_path"],
                    scene_description=panel_data["scene_description"],
                    target_emotion=panel_data.get("emotion")
                )
                results.append(result)

                # Brief status update
                status = "✅" if not result.issues else "⚠️"
                print(f"   {status} {Path(result.panel_path).name}: Score {result.overall_score:.2f}")

            except Exception as e:
                print(f"❌ Error validating panel {i}: {e}")
                # Create error result
                error_result = ValidationResult(
                    panel_path=panel_data["image_path"],
                    scene_description=panel_data["scene_description"],
                    scene_aligned=False,
                    scene_confidence=0.0,
                    scene_reasoning=f"Validation failed: {e}",
                    emotion_target=panel_data.get("emotion"),
                    emotion_detected="error",
                    emotion_aligned=False,
                    emotion_confidence=0.0,
                    emotion_reasoning=f"Validation failed: {e}",
                    dialogue_placement_ok=False,
                    dialogue_confidence=0.0,
                    dialogue_reasoning=f"Validation failed: {e}",
                    overall_score=0.0,
                    issues=["validation_error"],
                    timestamp=datetime.now().isoformat()
                )
                results.append(error_result)

        print(f"✅ Batch validation complete: {len(results)} panels processed")
        return results
