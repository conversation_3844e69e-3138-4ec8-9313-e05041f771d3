#!/usr/bin/env python3
"""
Final Evaluation Agent

Aggregates scores from multiple validation passes, calibrates for bias,
and generates comprehensive quality reports with VQA analysis.
"""

import os
import sys
import json
import argparse
import statistics
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import numpy as np

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from pipeline.utils import detect_faces, detect_blur, detect_pose_keypoints


class ScoreAggregator:
    """Aggregates and normalizes scores from multiple validation passes."""
    
    def __init__(self):
        self.panel_scores = {}
        self.chapter_scores = {}
        self.global_scores = {}
        
    def load_manga_results(self, manga_dir: str) -> Dict[str, Any]:
        """Load manga generation results."""
        manga_path = Path(manga_dir)
        results_file = manga_path / "manga_results.json"
        
        if not results_file.exists():
            raise FileNotFoundError(f"manga_results.json not found in {manga_dir}")
            
        with open(results_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def load_validation_results(self, manga_dir: str) -> Optional[Dict[str, Any]]:
        """Load validation results if they exist."""
        manga_path = Path(manga_dir)
        validation_file = manga_path / "validation_results.json"
        
        if validation_file.exists():
            with open(validation_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return None
    
    def load_fix_results(self, manga_dir: str) -> Optional[Dict[str, Any]]:
        """Load fix results if they exist."""
        manga_path = Path(manga_dir)
        fix_file = manga_path / "fix_results.json"
        
        if fix_file.exists():
            with open(fix_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return None
    
    def calculate_technical_scores(self, manga_results: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate technical quality scores for each panel."""
        technical_scores = {
            "panels": {},
            "chapters": {},
            "global": {}
        }
        
        all_panel_scores = []
        
        for chapter in manga_results.get("chapters", []):
            chapter_num = chapter.get("chapter_number", 0)
            chapter_panel_scores = []
            
            for scene in chapter.get("scenes", []):
                scene_num = scene.get("scene_number", 0)
                panel_id = f"chapter_{chapter_num}_scene_{scene_num}"
                
                # Calculate individual scores
                generation_score = 1.0 if scene.get("generation_success", False) else 0.0
                dialogue_score = 1.0 if scene.get("bubble_success", False) else 0.0
                
                # Technical quality assessment
                panel_path = scene.get("panel_path")
                technical_score = 0.0
                
                if panel_path and Path(panel_path).exists():
                    # Face detection score (0-1, penalize multiple faces)
                    face_count = detect_faces(panel_path)
                    face_score = 1.0 if face_count <= 1 else max(0.0, 1.0 - (face_count - 1) * 0.3)
                    
                    # Blur detection score (0-1)
                    blur_score = min(1.0, detect_blur(panel_path) / 200.0)
                    
                    # Pose detection score (0-1)
                    pose_keypoints = detect_pose_keypoints(panel_path)
                    pose_score = min(1.0, pose_keypoints / 10.0)
                    
                    technical_score = (face_score + blur_score + pose_score) / 3.0
                
                # Composite panel score
                panel_score = {
                    "generation_score": generation_score,
                    "dialogue_score": dialogue_score,
                    "technical_score": technical_score,
                    "composite_score": (generation_score + dialogue_score + technical_score) / 3.0,
                    "scene_description": scene.get("description", ""),
                    "emotion": scene.get("emotion", "neutral"),
                    "panel_path": panel_path
                }
                
                technical_scores["panels"][panel_id] = panel_score
                chapter_panel_scores.append(panel_score["composite_score"])
                all_panel_scores.append(panel_score["composite_score"])
            
            # Chapter-level scores
            if chapter_panel_scores:
                technical_scores["chapters"][f"chapter_{chapter_num}"] = {
                    "average_score": statistics.mean(chapter_panel_scores),
                    "min_score": min(chapter_panel_scores),
                    "max_score": max(chapter_panel_scores),
                    "panel_count": len(chapter_panel_scores),
                    "success_rate": sum(1 for score in chapter_panel_scores if score >= 0.7) / len(chapter_panel_scores)
                }
        
        # Global scores
        if all_panel_scores:
            technical_scores["global"] = {
                "average_score": statistics.mean(all_panel_scores),
                "median_score": statistics.median(all_panel_scores),
                "std_deviation": statistics.stdev(all_panel_scores) if len(all_panel_scores) > 1 else 0.0,
                "total_panels": len(all_panel_scores),
                "excellent_panels": sum(1 for score in all_panel_scores if score >= 0.8),
                "good_panels": sum(1 for score in all_panel_scores if 0.6 <= score < 0.8),
                "needs_fixing_panels": sum(1 for score in all_panel_scores if score < 0.6)
            }
        
        return technical_scores
    
    def aggregate_scores(self, manga_dir: str) -> Dict[str, Any]:
        """Main aggregation function."""
        print(f"🔍 Analyzing manga quality: {manga_dir}")
        
        # Load all available data
        manga_results = self.load_manga_results(manga_dir)
        validation_results = self.load_validation_results(manga_dir)
        fix_results = self.load_fix_results(manga_dir)
        
        # Calculate technical scores
        technical_scores = self.calculate_technical_scores(manga_results)
        
        # Aggregate final scores
        aggregated_scores = {
            "manga_title": manga_results.get("title", "Unknown"),
            "analysis_timestamp": datetime.now().isoformat(),
            "manga_directory": manga_dir,
            "technical_scores": technical_scores,
            "validation_scores": validation_results,
            "fix_scores": fix_results,
            "summary": self._generate_summary(technical_scores)
        }
        
        return aggregated_scores
    
    def _generate_summary(self, technical_scores: Dict[str, Any]) -> Dict[str, Any]:
        """Generate summary statistics."""
        global_scores = technical_scores.get("global", {})
        
        if not global_scores:
            return {"status": "no_data", "message": "No panels to analyze"}
        
        avg_score = global_scores.get("average_score", 0.0)
        total_panels = global_scores.get("total_panels", 0)
        excellent = global_scores.get("excellent_panels", 0)
        good = global_scores.get("good_panels", 0)
        needs_fixing = global_scores.get("needs_fixing_panels", 0)
        
        # Determine overall grade
        if avg_score >= 0.8:
            grade = "Excellent"
            grade_emoji = "🌟"
        elif avg_score >= 0.6:
            grade = "Good"
            grade_emoji = "✅"
        else:
            grade = "Needs Improvement"
            grade_emoji = "⚠️"
        
        return {
            "overall_grade": grade,
            "grade_emoji": grade_emoji,
            "average_score": round(avg_score, 3),
            "total_panels": total_panels,
            "quality_distribution": {
                "excellent": excellent,
                "good": good,
                "needs_fixing": needs_fixing
            },
            "success_rate": round((excellent + good) / total_panels * 100, 1) if total_panels > 0 else 0.0
        }


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Final manga evaluation and score aggregation",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python scripts/final_evaluator.py outputs/quality_manga_20250601_200706
    python scripts/final_evaluator.py outputs/fixed_manga_TIMESTAMP
        """
    )
    
    parser.add_argument(
        "manga_dir",
        help="Path to manga output directory"
    )
    
    parser.add_argument(
        "--output",
        help="Output file for aggregated scores (default: score_aggregation.json)"
    )
    
    args = parser.parse_args()
    
    try:
        # Initialize aggregator
        aggregator = ScoreAggregator()
        
        # Aggregate scores
        aggregated_scores = aggregator.aggregate_scores(args.manga_dir)
        
        # Save results
        output_file = args.output or Path(args.manga_dir) / "score_aggregation.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(aggregated_scores, f, indent=2)
        
        # Print summary
        summary = aggregated_scores.get("summary", {})
        print(f"\n📊 Final Evaluation Results:")
        print(f"   {summary.get('grade_emoji', '❓')} Overall Grade: {summary.get('overall_grade', 'Unknown')}")
        print(f"   📈 Average Score: {summary.get('average_score', 0.0):.3f}")
        print(f"   🎯 Success Rate: {summary.get('success_rate', 0.0):.1f}%")
        print(f"   📁 Results saved: {output_file}")
        
        return 0
        
    except Exception as e:
        print(f"❌ Evaluation failed: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
