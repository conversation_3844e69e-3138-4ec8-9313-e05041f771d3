#!/usr/bin/env python3
"""
Generate Quality Manga Script

Creates a proper manga with detailed story, dialogue, and high-quality images.
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from image_gen.image_generator import generate_image
from scripts.place_dialogue import place_dialogue
from scripts.compile_pdf import compile_manga_pdf


def create_detailed_story():
    """Create a detailed story structure with proper scenes and dialogue."""
    
    story_structure = {
        "title": "The Shadow Blade Chronicles",
        "chapters": [
            {
                "title": "Chapter 1: The Cursed Village",
                "scenes": [
                    {
                        "scene_number": 1,
                        "description": "A young samurai warrior stands at the edge of a cursed village. Dark clouds gather overhead as withered trees and abandoned houses create an ominous atmosphere. The samurai wears traditional armor and carries a katana, looking determined despite the foreboding environment.",
                        "dialogue": ["The curse has spread further than I thought...", "I must find the source before it's too late."],
                        "emotion": "determined",
                        "estimated_panels": 1
                    },
                    {
                        "scene_number": 2,
                        "description": "The samurai encounters a mysterious old sage in the village center. The sage has glowing eyes and wears tattered robes, standing near an ancient stone monument covered in mystical symbols. Magical energy swirls around them.",
                        "dialogue": ["Young warrior, you seek the Crystal Sword?", "Only one pure of heart can wield its power!"],
                        "emotion": "mystical",
                        "estimated_panels": 1
                    },
                    {
                        "scene_number": 3,
                        "description": "The samurai kneels before the sage in respect, showing humility and determination. The background shows the cursed village with dead trees and dark sky, emphasizing the urgency of their quest.",
                        "dialogue": ["I will do whatever it takes to save my people.", "Tell me where I can find this legendary blade."],
                        "emotion": "respectful",
                        "estimated_panels": 1
                    }
                ]
            },
            {
                "title": "Chapter 2: The Sacred Mountain",
                "scenes": [
                    {
                        "scene_number": 4,
                        "description": "The samurai climbs a treacherous mountain path. Sharp rocks and steep cliffs surround them, with mist swirling around the peaks. The warrior shows strain but determination as they ascend toward their destiny.",
                        "dialogue": ["The path grows more dangerous...", "But I cannot turn back now!"],
                        "emotion": "determined",
                        "estimated_panels": 1
                    },
                    {
                        "scene_number": 5,
                        "description": "At the mountain peak, the samurai faces a massive stone guardian with glowing red eyes. The guardian blocks the entrance to a sacred temple where the Crystal Sword awaits. Lightning crackles in the stormy sky above.",
                        "dialogue": ["Who dares seek the Crystal Sword?", "I am here to save my village from the curse!"],
                        "emotion": "confrontational",
                        "estimated_panels": 1
                    },
                    {
                        "scene_number": 6,
                        "description": "Epic battle scene as the samurai fights the stone guardian. Dynamic action with sword clashes, magical energy, and debris flying. The warrior shows skill and courage against the massive opponent.",
                        "dialogue": ["Your strength is impressive, mortal!", "I fight for those who cannot fight for themselves!"],
                        "emotion": "intense",
                        "estimated_panels": 1
                    }
                ]
            },
            {
                "title": "Chapter 3: The Crystal Sword",
                "scenes": [
                    {
                        "scene_number": 7,
                        "description": "Inside the sacred temple, the samurai approaches a pedestal where the Crystal Sword floats in a beam of pure light. Ancient pillars and mystical symbols surround the holy weapon, creating an atmosphere of divine power.",
                        "dialogue": ["At last... the legendary Crystal Sword.", "May I prove worthy of its power."],
                        "emotion": "awestruck",
                        "estimated_panels": 1
                    },
                    {
                        "scene_number": 8,
                        "description": "The samurai grasps the Crystal Sword and it begins to glow with brilliant energy. Magical light emanates from the blade, transforming the warrior's appearance and filling them with divine power.",
                        "dialogue": ["The sword... it accepts me!", "I can feel its incredible power!"],
                        "emotion": "triumphant",
                        "estimated_panels": 1
                    },
                    {
                        "scene_number": 9,
                        "description": "The empowered samurai returns to their village, now healed and restored. Green grass grows where there was once decay, and villagers emerge from their homes with joy and gratitude. The curse has been broken.",
                        "dialogue": ["The curse is broken! Our village is saved!", "Thank you, brave warrior, for your sacrifice!"],
                        "emotion": "joyful",
                        "estimated_panels": 1
                    }
                ]
            }
        ],
        "total_scenes": 9,
        "estimated_total_panels": 9,
        "original_prompt": "A magical samurai seeks the legendary crystal sword to save their village from an ancient curse",
        "generation_time": datetime.now().isoformat(),
        "fallback": False
    }
    
    return story_structure


def generate_quality_manga():
    """Generate a high-quality manga with proper story and dialogue."""
    
    print("🎨 Generating Quality Manga with Proper Story & Dialogue")
    print("=" * 60)
    
    # Create output directory
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = Path("outputs") / f"quality_manga_{timestamp}"
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"📁 Output directory: {output_dir}")
    
    # Create detailed story structure
    print(f"\n📖 Creating detailed story structure...")
    story_structure = create_detailed_story()
    
    # Save story structure
    story_file = output_dir / "story_structure.json"
    with open(story_file, 'w', encoding='utf-8') as f:
        json.dump(story_structure, f, indent=2)
    
    print(f"✅ Story structure saved: {story_file}")
    print(f"   Title: {story_structure['title']}")
    print(f"   Chapters: {len(story_structure['chapters'])}")
    print(f"   Total scenes: {story_structure['total_scenes']}")
    
    # Generate manga panels
    print(f"\n🎨 Generating manga panels...")
    
    manga_results = {
        "title": story_structure["title"],
        "generation_timestamp": datetime.now().isoformat(),
        "chapters": []
    }
    
    panel_count = 0
    
    for chapter_idx, chapter in enumerate(story_structure["chapters"], 1):
        print(f"\n📚 Chapter {chapter_idx}: {chapter['title']}")
        
        # Create chapter directory
        chapter_dir = output_dir / f"chapter_{chapter_idx:02d}"
        chapter_dir.mkdir(exist_ok=True)
        
        bubble_dir = chapter_dir / "with_bubbles"
        bubble_dir.mkdir(exist_ok=True)
        
        chapter_results = {
            "chapter_number": chapter_idx,
            "title": chapter["title"],
            "scenes": []
        }
        
        for scene in chapter["scenes"]:
            panel_count += 1
            scene_num = scene["scene_number"]
            
            print(f"   🎬 Scene {scene_num}: Generating panel...")
            
            # Create enhanced prompt for image generation
            enhanced_prompt = f"""manga style, black and white, detailed lineart, {scene['description']}, 
            high quality, masterpiece, best quality, traditional Japanese art style, 
            dramatic composition, detailed character design, atmospheric lighting"""
            
            # Generate base panel
            panel_path = chapter_dir / f"scene_{scene_num:02d}.png"
            
            try:
                print(f"      🖼️  Generating image...")
                generated_path = generate_image(
                    prompt=enhanced_prompt,
                    index=scene_num,
                    output_dir=str(chapter_dir)
                )
                
                if generated_path and Path(generated_path).exists():
                    # Move to correct filename if needed
                    if str(generated_path) != str(panel_path):
                        Path(generated_path).rename(panel_path)
                    
                    print(f"      ✅ Panel generated: {panel_path.name}")
                    generation_success = True
                else:
                    print(f"      ❌ Panel generation failed")
                    generation_success = False
                
            except Exception as e:
                print(f"      ❌ Panel generation error: {e}")
                generation_success = False
            
            # Add dialogue bubbles
            bubble_panel_path = bubble_dir / f"scene_{scene_num:02d}_bubble.png"
            bubble_success = False
            
            if generation_success and panel_path.exists():
                try:
                    print(f"      💬 Adding dialogue bubbles...")
                    dialogue_lines = scene.get("dialogue", [])
                    
                    if dialogue_lines:
                        bubble_success = place_dialogue(
                            str(panel_path),
                            dialogue_lines,
                            str(bubble_panel_path)
                        )
                        
                        if bubble_success:
                            print(f"      ✅ Dialogue added: {bubble_panel_path.name}")
                        else:
                            print(f"      ❌ Dialogue placement failed")
                    else:
                        # Copy panel without dialogue
                        import shutil
                        shutil.copy2(panel_path, bubble_panel_path)
                        bubble_success = True
                        print(f"      ➖ No dialogue for this scene")
                        
                except Exception as e:
                    print(f"      ❌ Dialogue error: {e}")
            
            # Record scene results
            scene_results = {
                "scene_number": scene_num,
                "description": scene["description"],
                "dialogue_lines": scene.get("dialogue", []),
                "emotion": scene.get("emotion", "neutral"),
                "panel_path": str(panel_path),
                "bubble_panel_path": str(bubble_panel_path),
                "generation_success": generation_success,
                "bubble_success": bubble_success,
                "enhanced_prompt": enhanced_prompt
            }
            
            chapter_results["scenes"].append(scene_results)
        
        manga_results["chapters"].append(chapter_results)
    
    # Save manga results
    results_file = output_dir / "manga_results.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(manga_results, f, indent=2)
    
    print(f"\n💾 Manga results saved: {results_file}")
    
    # Compile PDFs
    print(f"\n📄 Compiling manga PDFs...")
    
    try:
        # Compile base manga
        base_pdf = compile_manga_pdf(str(output_dir), include_dialogue=False)
        print(f"✅ Base manga PDF: {base_pdf}")
        
        # Compile manga with dialogue
        dialogue_pdf = compile_manga_pdf(str(output_dir), include_dialogue=True)
        print(f"✅ Dialogue manga PDF: {dialogue_pdf}")
        
    except Exception as e:
        print(f"❌ PDF compilation error: {e}")
    
    # Summary
    successful_panels = sum(1 for chapter in manga_results["chapters"] 
                          for scene in chapter["scenes"] 
                          if scene["generation_success"])
    
    successful_bubbles = sum(1 for chapter in manga_results["chapters"] 
                           for scene in chapter["scenes"] 
                           if scene["bubble_success"])
    
    print(f"\n🎉 Quality Manga Generation Complete!")
    print(f"📊 Summary:")
    print(f"   Total panels: {panel_count}")
    print(f"   Successful generations: {successful_panels}/{panel_count}")
    print(f"   Successful dialogue: {successful_bubbles}/{panel_count}")
    print(f"📁 Output: {output_dir}")
    
    return str(output_dir)


if __name__ == "__main__":
    try:
        manga_dir = generate_quality_manga()
        print(f"\n✅ Quality manga generated successfully!")
        print(f"📁 Directory: {manga_dir}")
        
    except Exception as e:
        print(f"❌ Quality manga generation failed: {e}")
        sys.exit(1)
