"""
Issue Analyzer for Manga Validation Results

Analyzes validation results to identify fixable issues and generate improvement strategies.
"""

import json
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from validation.vlm_validator import ValidationResult


@dataclass
class FixStrategy:
    """Strategy for fixing a specific issue."""
    issue_type: str
    panel_path: str
    original_score: float
    fix_method: str
    new_prompt: Optional[str] = None
    dialogue_adjustments: Optional[Dict[str, Any]] = None
    regenerate_image: bool = False
    priority: int = 1  # 1=high, 2=medium, 3=low


class IssueAnalyzer:
    """
    Analyzes validation results and generates fix strategies.
    """
    
    def __init__(self, confidence_threshold: float = 0.75):
        """
        Initialize the issue analyzer.
        
        Args:
            confidence_threshold: Minimum confidence score to consider acceptable
        """
        self.confidence_threshold = confidence_threshold
        
    def load_validation_results(self, results_path: str) -> List[ValidationResult]:
        """
        Load validation results from JSON file.
        
        Args:
            results_path: Path to validation_results.json
            
        Returns:
            List of ValidationResult objects
        """
        with open(results_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        results = []
        for result_data in data["results"]:
            result = ValidationResult(
                panel_path=result_data["panel_path"],
                scene_description=result_data["scene_description"],
                scene_aligned=result_data["scene_aligned"],
                scene_confidence=result_data["scene_confidence"],
                scene_reasoning=result_data["scene_reasoning"],
                emotion_target=result_data["emotion_target"],
                emotion_detected=result_data["emotion_detected"],
                emotion_aligned=result_data["emotion_aligned"],
                emotion_confidence=result_data["emotion_confidence"],
                emotion_reasoning=result_data["emotion_reasoning"],
                dialogue_placement_ok=result_data["dialogue_placement_ok"],
                dialogue_confidence=result_data["dialogue_confidence"],
                dialogue_reasoning=result_data["dialogue_reasoning"],
                overall_score=result_data["overall_score"],
                issues=result_data["issues"],
                timestamp=result_data["timestamp"]
            )
            results.append(result)
        
        return results
    
    def identify_fixable_issues(self, results: List[ValidationResult]) -> List[FixStrategy]:
        """
        Identify issues that can be automatically fixed.
        
        Args:
            results: List of validation results
            
        Returns:
            List of fix strategies
        """
        fix_strategies = []
        
        for result in results:
            # Skip panels that are already good enough
            if result.overall_score >= self.confidence_threshold and not result.issues:
                continue
            
            # Analyze each type of issue
            strategies = self._analyze_panel_issues(result)
            fix_strategies.extend(strategies)
        
        # Sort by priority (high priority first)
        fix_strategies.sort(key=lambda x: x.priority)
        
        return fix_strategies
    
    def _analyze_panel_issues(self, result: ValidationResult) -> List[FixStrategy]:
        """
        Analyze issues for a single panel and generate fix strategies.
        
        Args:
            result: Validation result for a single panel
            
        Returns:
            List of fix strategies for this panel
        """
        strategies = []
        
        # Dialogue placement issues
        if "dialogue_placement" in result.issues:
            strategy = self._create_dialogue_fix_strategy(result)
            if strategy:
                strategies.append(strategy)
        
        # Scene mismatch issues
        if "scene_mismatch" in result.issues:
            strategy = self._create_scene_fix_strategy(result)
            if strategy:
                strategies.append(strategy)
        
        # Emotion mismatch issues
        if "emotion_mismatch" in result.issues:
            strategy = self._create_emotion_fix_strategy(result)
            if strategy:
                strategies.append(strategy)
        
        # Low confidence issues
        if result.overall_score < self.confidence_threshold:
            strategy = self._create_quality_improvement_strategy(result)
            if strategy:
                strategies.append(strategy)
        
        return strategies
    
    def _create_dialogue_fix_strategy(self, result: ValidationResult) -> Optional[FixStrategy]:
        """
        Create fix strategy for dialogue placement issues.
        
        Args:
            result: Validation result
            
        Returns:
            Fix strategy or None
        """
        # Analyze the dialogue reasoning to understand the issue
        reasoning = result.dialogue_reasoning.lower()
        
        dialogue_adjustments = {}
        
        if "tail" in reasoning and "point" in reasoning:
            dialogue_adjustments["add_tails"] = True
            dialogue_adjustments["position_near_speaker"] = True
        
        if "cover" in reasoning and ("face" in reasoning or "background" in reasoning):
            dialogue_adjustments["avoid_important_areas"] = True
            dialogue_adjustments["use_face_detection"] = True
        
        if "unclear" in reasoning and "speaking" in reasoning:
            dialogue_adjustments["position_near_speaker"] = True
            dialogue_adjustments["add_tails"] = True
        
        return FixStrategy(
            issue_type="dialogue_placement",
            panel_path=result.panel_path,
            original_score=result.dialogue_confidence,
            fix_method="adjust_bubble_placement",
            dialogue_adjustments=dialogue_adjustments,
            regenerate_image=False,
            priority=1  # High priority - easy fix
        )
    
    def _create_scene_fix_strategy(self, result: ValidationResult) -> Optional[FixStrategy]:
        """
        Create fix strategy for scene mismatch issues.
        
        Args:
            result: Validation result
            
        Returns:
            Fix strategy or None
        """
        reasoning = result.scene_reasoning.lower()
        
        # Check if the issue is vague description vs actual mismatch
        if "vague" in reasoning or "no specific details" in reasoning:
            # Need better scene description, not regeneration
            enhanced_prompt = self._enhance_scene_description(result.scene_description, reasoning)
            
            return FixStrategy(
                issue_type="scene_mismatch",
                panel_path=result.panel_path,
                original_score=result.scene_confidence,
                fix_method="enhance_scene_description",
                new_prompt=enhanced_prompt,
                regenerate_image=False,
                priority=2  # Medium priority
            )
        else:
            # Actual scene mismatch - need regeneration
            enhanced_prompt = self._create_detailed_scene_prompt(result)
            
            return FixStrategy(
                issue_type="scene_mismatch",
                panel_path=result.panel_path,
                original_score=result.scene_confidence,
                fix_method="regenerate_with_enhanced_prompt",
                new_prompt=enhanced_prompt,
                regenerate_image=True,
                priority=2  # Medium priority - requires regeneration
            )
    
    def _create_emotion_fix_strategy(self, result: ValidationResult) -> Optional[FixStrategy]:
        """
        Create fix strategy for emotion mismatch issues.
        
        Args:
            result: Validation result
            
        Returns:
            Fix strategy or None
        """
        if not result.emotion_target:
            return None
        
        # Create emotion-enhanced prompt
        emotion_prompt = self._enhance_prompt_with_emotion(
            result.scene_description,
            result.emotion_target,
            result.emotion_detected
        )
        
        return FixStrategy(
            issue_type="emotion_mismatch",
            panel_path=result.panel_path,
            original_score=result.emotion_confidence,
            fix_method="regenerate_with_emotion_cues",
            new_prompt=emotion_prompt,
            regenerate_image=True,
            priority=2  # Medium priority
        )
    
    def _create_quality_improvement_strategy(self, result: ValidationResult) -> Optional[FixStrategy]:
        """
        Create fix strategy for general quality improvement.
        
        Args:
            result: Validation result
            
        Returns:
            Fix strategy or None
        """
        # Find the lowest scoring aspect
        scores = {
            "scene": result.scene_confidence,
            "emotion": result.emotion_confidence,
            "dialogue": result.dialogue_confidence
        }
        
        lowest_aspect = min(scores, key=scores.get)
        
        if lowest_aspect == "dialogue" and result.dialogue_confidence < self.confidence_threshold:
            return self._create_dialogue_fix_strategy(result)
        elif lowest_aspect == "scene" and result.scene_confidence < self.confidence_threshold:
            return self._create_scene_fix_strategy(result)
        elif lowest_aspect == "emotion" and result.emotion_confidence < self.confidence_threshold:
            return self._create_emotion_fix_strategy(result)
        
        return None
    
    def _enhance_scene_description(self, original_description: str, reasoning: str) -> str:
        """
        Enhance a vague scene description with more details.
        
        Args:
            original_description: Original scene description
            reasoning: VLM reasoning about why it failed
            
        Returns:
            Enhanced scene description
        """
        # Extract chapter/scene info
        if "chapter" in original_description.lower():
            chapter_match = original_description.lower()
            
            # Add generic manga scene details based on chapter
            if "chapter_01" in chapter_match:
                return "A young ninja warrior approaches a hidden village entrance. Traditional Japanese architecture with wooden buildings and tiled roofs. The character wears dark ninja clothing and carries weapons, showing determination and readiness for adventure."
            elif "chapter_02" in chapter_match:
                return "Action scene with ninja characters in combat. Dynamic poses with weapons drawn, traditional Japanese village setting in background. Characters show intense emotions during confrontation."
            elif "chapter_03" in chapter_match:
                return "Dramatic scene showing character development. Emotional moment with detailed character expressions, traditional Japanese environment with natural elements like trees or mountains."
        
        # Generic enhancement
        return f"Detailed manga scene: {original_description}. Traditional Japanese setting with clear character poses, detailed facial expressions, and dynamic composition suitable for manga storytelling."
    
    def _create_detailed_scene_prompt(self, result: ValidationResult) -> str:
        """
        Create a detailed scene prompt for regeneration.
        
        Args:
            result: Validation result
            
        Returns:
            Detailed scene prompt
        """
        base_prompt = "manga style, black and white, detailed lineart, "
        
        # Add scene-specific details based on panel path
        panel_name = Path(result.panel_path).name
        
        if "scene_01" in panel_name:
            scene_details = "establishing shot, character introduction, wide view of setting"
        elif "scene_02" in panel_name:
            scene_details = "action scene, dynamic poses, medium shot with character interaction"
        elif "scene_03" in panel_name:
            scene_details = "dramatic moment, close-up on character emotions, detailed facial expressions"
        else:
            scene_details = "manga panel with clear composition and character focus"
        
        # Add emotion if available
        emotion_cue = ""
        if result.emotion_target:
            emotion_cue = f", {result.emotion_target} expression"
        
        return f"{base_prompt}{scene_details}{emotion_cue}, traditional Japanese setting, high quality manga artwork"
    
    def _enhance_prompt_with_emotion(self, scene_description: str, target_emotion: str, detected_emotion: str) -> str:
        """
        Enhance prompt with specific emotion cues.
        
        Args:
            scene_description: Original scene description
            target_emotion: Target emotion
            detected_emotion: Currently detected emotion
            
        Returns:
            Emotion-enhanced prompt
        """
        emotion_cues = {
            "determined": "focused expression, clenched fists, forward-leaning posture, intense eyes",
            "tense": "alert stance, raised weapons, worried expression, dynamic action pose",
            "desperate": "pleading gesture, distressed expression, vulnerable posture, emotional intensity",
            "angry": "furrowed brow, clenched teeth, aggressive stance, intense glare",
            "sad": "downcast eyes, slumped shoulders, melancholic expression, tears",
            "happy": "bright smile, relaxed posture, cheerful expression, positive body language"
        }
        
        emotion_detail = emotion_cues.get(target_emotion, f"{target_emotion} expression")
        
        return f"manga style, {scene_description}, character with {emotion_detail}, detailed facial features, emotional storytelling, black and white lineart"
