"""
Auto-Fix Engine for Manga Panel Issues

Implements automated fixes for issues identified by the VLM validator.
"""

import os
import json
import shutil
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime
import cv2
import numpy as np

from auto_fix.issue_analyzer import FixStrategy, IssueAnalyzer
from scripts.place_dialogue import place_dialogue, detect_face_bbox
from image_gen.image_generator import generate_image
from image_gen.comfy_client import ComfyUIClient


class AutoFixEngine:
    """
    Automated fix engine for manga panel issues.
    """
    
    def __init__(self, output_base_dir: str = "outputs"):
        """
        Initialize the auto-fix engine.
        
        Args:
            output_base_dir: Base directory for outputs
        """
        self.output_base_dir = Path(output_base_dir)
        self.comfy_client = ComfyUIClient()
        
    def apply_fixes(self, 
                   fix_strategies: List[FixStrategy], 
                   original_manga_dir: str) -> str:
        """
        Apply all fix strategies and create a fixed manga version.
        
        Args:
            fix_strategies: List of fix strategies to apply
            original_manga_dir: Path to original manga directory
            
        Returns:
            Path to fixed manga directory
        """
        original_path = Path(original_manga_dir)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        fixed_dir = self.output_base_dir / f"fixed_manga_{timestamp}"
        
        print(f"🔧 Starting auto-fix process...")
        print(f"📁 Original: {original_path}")
        print(f"📁 Fixed: {fixed_dir}")
        print(f"🎯 Applying {len(fix_strategies)} fixes...")
        
        # Create fixed manga directory structure
        self._setup_fixed_directory(original_path, fixed_dir)
        
        # Apply each fix strategy
        fix_results = []
        for i, strategy in enumerate(fix_strategies, 1):
            print(f"\n🔧 Fix {i}/{len(fix_strategies)}: {strategy.issue_type}")
            print(f"   Panel: {Path(strategy.panel_path).name}")
            print(f"   Method: {strategy.fix_method}")
            
            try:
                result = self._apply_single_fix(strategy, original_path, fixed_dir)
                fix_results.append(result)
                
                if result["success"]:
                    print(f"   ✅ Fix applied successfully")
                else:
                    print(f"   ❌ Fix failed: {result['error']}")
                    
            except Exception as e:
                print(f"   ❌ Fix failed with exception: {e}")
                fix_results.append({
                    "strategy": strategy,
                    "success": False,
                    "error": str(e),
                    "fixed_path": None
                })
        
        # Save fix results
        self._save_fix_results(fix_results, fixed_dir)
        
        print(f"\n✅ Auto-fix process complete!")
        print(f"📁 Fixed manga saved to: {fixed_dir}")
        
        return str(fixed_dir)
    
    def _setup_fixed_directory(self, original_path: Path, fixed_dir: Path):
        """
        Set up the fixed manga directory structure.
        
        Args:
            original_path: Original manga directory
            fixed_dir: Fixed manga directory to create
        """
        fixed_dir.mkdir(parents=True, exist_ok=True)
        
        # Copy metadata files
        for metadata_file in ["story_structure.json", "manga_results.json"]:
            src = original_path / metadata_file
            if src.exists():
                shutil.copy2(src, fixed_dir / metadata_file)
        
        # Copy chapter structure
        for chapter_dir in original_path.glob("chapter_*"):
            fixed_chapter_dir = fixed_dir / chapter_dir.name
            fixed_chapter_dir.mkdir(exist_ok=True)
            
            # Copy original panels (will be replaced by fixes if needed)
            for panel_file in chapter_dir.glob("scene_*.png"):
                shutil.copy2(panel_file, fixed_chapter_dir / panel_file.name)
            
            # Copy bubble directory structure
            bubble_dir = chapter_dir / "with_bubbles"
            if bubble_dir.exists():
                fixed_bubble_dir = fixed_chapter_dir / "with_bubbles"
                fixed_bubble_dir.mkdir(exist_ok=True)
                
                for bubble_file in bubble_dir.glob("*_bubble.png"):
                    shutil.copy2(bubble_file, fixed_bubble_dir / bubble_file.name)
    
    def _apply_single_fix(self, 
                         strategy: FixStrategy, 
                         original_path: Path, 
                         fixed_dir: Path) -> Dict[str, Any]:
        """
        Apply a single fix strategy.
        
        Args:
            strategy: Fix strategy to apply
            original_path: Original manga directory
            fixed_dir: Fixed manga directory
            
        Returns:
            Fix result dictionary
        """
        try:
            if strategy.fix_method == "adjust_bubble_placement":
                return self._fix_dialogue_placement(strategy, original_path, fixed_dir)
            
            elif strategy.fix_method == "enhance_scene_description":
                return self._fix_scene_description(strategy, original_path, fixed_dir)
            
            elif strategy.fix_method == "regenerate_with_enhanced_prompt":
                return self._regenerate_with_prompt(strategy, original_path, fixed_dir)
            
            elif strategy.fix_method == "regenerate_with_emotion_cues":
                return self._regenerate_with_emotion(strategy, original_path, fixed_dir)
            
            else:
                return {
                    "strategy": strategy,
                    "success": False,
                    "error": f"Unknown fix method: {strategy.fix_method}",
                    "fixed_path": None
                }
                
        except Exception as e:
            return {
                "strategy": strategy,
                "success": False,
                "error": str(e),
                "fixed_path": None
            }
    
    def _fix_dialogue_placement(self, 
                               strategy: FixStrategy, 
                               original_path: Path, 
                               fixed_dir: Path) -> Dict[str, Any]:
        """
        Fix dialogue bubble placement issues.
        
        Args:
            strategy: Fix strategy
            original_path: Original manga directory
            fixed_dir: Fixed manga directory
            
        Returns:
            Fix result
        """
        # Find the original panel without bubbles
        panel_path = Path(strategy.panel_path)
        
        # Determine source and target paths
        if "with_bubbles" in str(panel_path):
            # Find original panel without bubbles
            chapter_name = panel_path.parent.parent.name
            scene_name = panel_path.name.replace("_bubble", "")
            source_panel = original_path / chapter_name / scene_name
            target_panel = fixed_dir / chapter_name / "with_bubbles" / panel_path.name
        else:
            source_panel = panel_path
            target_panel = fixed_dir / panel_path.relative_to(original_path)
        
        if not source_panel.exists():
            return {
                "strategy": strategy,
                "success": False,
                "error": f"Source panel not found: {source_panel}",
                "fixed_path": None
            }
        
        # Extract dialogue from original bubble panel if it exists
        dialogue_lines = self._extract_dialogue_from_panel(strategy.panel_path)
        
        if not dialogue_lines:
            # Use default dialogue for testing
            dialogue_lines = ["Fixed dialogue!", "Better placement!"]
        
        # Apply improved dialogue placement
        target_panel.parent.mkdir(parents=True, exist_ok=True)
        success = self._place_dialogue_improved(
            str(source_panel), 
            dialogue_lines, 
            str(target_panel),
            strategy.dialogue_adjustments or {}
        )
        
        return {
            "strategy": strategy,
            "success": success,
            "error": None if success else "Dialogue placement failed",
            "fixed_path": str(target_panel) if success else None
        }
    
    def _fix_scene_description(self, 
                              strategy: FixStrategy, 
                              original_path: Path, 
                              fixed_dir: Path) -> Dict[str, Any]:
        """
        Fix scene description (metadata only, no regeneration).
        
        Args:
            strategy: Fix strategy
            original_path: Original manga directory
            fixed_dir: Fixed manga directory
            
        Returns:
            Fix result
        """
        # Update story structure with enhanced description
        story_file = fixed_dir / "story_structure.json"
        
        if story_file.exists():
            with open(story_file, 'r', encoding='utf-8') as f:
                story_data = json.load(f)
            
            # Find and update the relevant scene description
            panel_path = Path(strategy.panel_path)
            chapter_name = None
            scene_name = None
            
            for part in panel_path.parts:
                if part.startswith("chapter_"):
                    chapter_name = part
                elif part.startswith("scene_"):
                    scene_name = part.replace(".png", "").replace("_bubble", "")
            
            if chapter_name and scene_name and "chapters" in story_data:
                for chapter in story_data["chapters"]:
                    if chapter_name.replace("chapter_", "").zfill(2) in chapter.get("title", ""):
                        if "scenes" in chapter:
                            scene_index = int(scene_name.split("_")[-1]) - 1
                            if 0 <= scene_index < len(chapter["scenes"]):
                                chapter["scenes"][scene_index]["description"] = strategy.new_prompt
                                break
            
            # Save updated story structure
            with open(story_file, 'w', encoding='utf-8') as f:
                json.dump(story_data, f, indent=2)
        
        return {
            "strategy": strategy,
            "success": True,
            "error": None,
            "fixed_path": str(story_file)
        }
    
    def _regenerate_with_prompt(self, 
                               strategy: FixStrategy, 
                               original_path: Path, 
                               fixed_dir: Path) -> Dict[str, Any]:
        """
        Regenerate panel with enhanced prompt.
        
        Args:
            strategy: Fix strategy
            original_path: Original manga directory
            fixed_dir: Fixed manga directory
            
        Returns:
            Fix result
        """
        if not self.comfy_client.is_server_ready():
            return {
                "strategy": strategy,
                "success": False,
                "error": "ComfyUI server not available",
                "fixed_path": None
            }
        
        # Determine target path
        panel_path = Path(strategy.panel_path)
        target_panel = fixed_dir / panel_path.relative_to(original_path)
        target_panel.parent.mkdir(parents=True, exist_ok=True)
        
        # Extract panel index for generation
        scene_name = panel_path.stem
        panel_index = int(scene_name.split("_")[-1]) if "_" in scene_name else 1
        
        try:
            # Generate new image with enhanced prompt
            generated_path = generate_image(
                prompt=strategy.new_prompt,
                index=panel_index,
                output_dir=str(target_panel.parent)
            )
            
            # Move to correct filename
            if generated_path and Path(generated_path).exists():
                final_path = target_panel
                shutil.move(generated_path, final_path)
                
                return {
                    "strategy": strategy,
                    "success": True,
                    "error": None,
                    "fixed_path": str(final_path)
                }
            else:
                return {
                    "strategy": strategy,
                    "success": False,
                    "error": "Image generation failed",
                    "fixed_path": None
                }
                
        except Exception as e:
            return {
                "strategy": strategy,
                "success": False,
                "error": f"Regeneration failed: {e}",
                "fixed_path": None
            }
    
    def _regenerate_with_emotion(self, 
                                strategy: FixStrategy, 
                                original_path: Path, 
                                fixed_dir: Path) -> Dict[str, Any]:
        """
        Regenerate panel with emotion-enhanced prompt.
        
        Args:
            strategy: Fix strategy
            original_path: Original manga directory
            fixed_dir: Fixed manga directory
            
        Returns:
            Fix result
        """
        # Same as regenerate_with_prompt but with emotion focus
        return self._regenerate_with_prompt(strategy, original_path, fixed_dir)
    
    def _extract_dialogue_from_panel(self, panel_path: str) -> List[str]:
        """
        Extract dialogue text from an existing bubble panel.
        
        Args:
            panel_path: Path to panel with dialogue bubbles
            
        Returns:
            List of dialogue lines (placeholder implementation)
        """
        # This is a placeholder - in a real implementation, you'd use OCR
        # For now, return some default dialogue based on the panel
        panel_name = Path(panel_path).name
        
        if "scene_01" in panel_name:
            return ["I must find the hidden village!", "My destiny awaits..."]
        elif "scene_02" in panel_name:
            return ["Who goes there?", "Show yourself, stranger!"]
        elif "scene_03" in panel_name:
            return ["I seek the hidden village...", "Please, I need your help!"]
        else:
            return ["Fixed dialogue!", "Better placement!"]
    
    def _place_dialogue_improved(self, 
                                source_path: str, 
                                dialogue_lines: List[str], 
                                target_path: str,
                                adjustments: Dict[str, Any]) -> bool:
        """
        Place dialogue with improved positioning based on adjustments.
        
        Args:
            source_path: Source panel path
            dialogue_lines: Dialogue lines to add
            target_path: Target path for panel with bubbles
            adjustments: Dialogue placement adjustments
            
        Returns:
            True if successful
        """
        try:
            # Load image
            img = cv2.imread(source_path)
            if img is None:
                return False
            
            h, w, _ = img.shape
            
            # Improved face detection and positioning
            face = detect_face_bbox(img)
            
            if face and adjustments.get("use_face_detection", True):
                fx, fy, fw, fh = face
                face_center_x = fx + fw // 2
                face_center_y = fy + fh // 2
                
                # Position bubble to avoid face if requested
                if adjustments.get("avoid_important_areas", False):
                    # Place bubble in opposite corner from face
                    if face_center_x < w // 2:
                        bubble_x = int(w * 0.6)  # Right side
                    else:
                        bubble_x = int(w * 0.1)  # Left side
                    
                    if face_center_y < h // 2:
                        bubble_y = int(h * 0.6)  # Bottom
                    else:
                        bubble_y = int(h * 0.1)  # Top
                else:
                    # Position near speaker but not covering face
                    if adjustments.get("position_near_speaker", False):
                        bubble_x = max(10, fx - 100) if face_center_x > w // 2 else min(w - 200, fx + fw + 10)
                        bubble_y = max(10, fy - 50)
                    else:
                        bubble_x = int(w * 0.1)
                        bubble_y = int(h * 0.1)
            else:
                # Default positioning
                bubble_x = int(w * 0.1)
                bubble_y = int(h * 0.1)
            
            # Use the existing place_dialogue function with improved positioning
            # For now, just call the original function
            return place_dialogue(source_path, dialogue_lines, target_path)
            
        except Exception as e:
            print(f"Error in improved dialogue placement: {e}")
            return False
    
    def _save_fix_results(self, fix_results: List[Dict[str, Any]], fixed_dir: Path):
        """
        Save fix results to JSON file.
        
        Args:
            fix_results: List of fix results
            fixed_dir: Fixed manga directory
        """
        # Convert fix results to serializable format
        serializable_results = []
        
        for result in fix_results:
            strategy = result["strategy"]
            serializable_result = {
                "issue_type": strategy.issue_type,
                "panel_path": strategy.panel_path,
                "original_score": strategy.original_score,
                "fix_method": strategy.fix_method,
                "priority": strategy.priority,
                "success": result["success"],
                "error": result["error"],
                "fixed_path": result["fixed_path"],
                "new_prompt": strategy.new_prompt,
                "dialogue_adjustments": strategy.dialogue_adjustments,
                "regenerate_image": strategy.regenerate_image
            }
            serializable_results.append(serializable_result)
        
        # Save to file
        fix_results_file = fixed_dir / "fix_results.json"
        with open(fix_results_file, 'w', encoding='utf-8') as f:
            json.dump({
                "timestamp": datetime.now().isoformat(),
                "total_fixes": len(fix_results),
                "successful_fixes": sum(1 for r in fix_results if r["success"]),
                "failed_fixes": sum(1 for r in fix_results if not r["success"]),
                "fixes": serializable_results
            }, f, indent=2)
        
        print(f"💾 Fix results saved: {fix_results_file}")
