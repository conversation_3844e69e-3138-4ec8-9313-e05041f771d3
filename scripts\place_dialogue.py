#!/usr/bin/env python3
"""
Dialogue Bubble Overlay Script

Places dialogue bubbles on manga panels with automatic positioning based on character faces.
"""

import cv2
from pathlib import Path
from typing import List
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load face cascade (local OpenCV XML file)
face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')


def detect_face_bbox(img):
    """
    Detect face bounding box in image.

    Args:
        img: OpenCV image array

    Returns:
        Tuple of (x, y, w, h) for largest face, or None if no face found
    """
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    faces = face_cascade.detectMultiScale(gray, scaleFactor=1.1, minNeighbors=5)
    if len(faces) == 0:
        return None
    # Return the largest face
    x, y, w, h = max(faces, key=lambda f: f[2] * f[3])
    return (x, y, w, h)


# Removed unused PIL-based functions - using OpenCV implementation instead


def place_dialogue(panel_path: str, dialogue_lines: List[str], output_path: str) -> bool:
    """
    Place dialogue bubbles on a manga panel using OpenCV.

    Args:
        panel_path: Path to the original panel image
        dialogue_lines: List of dialogue lines
        output_path: Path to save the panel with dialogue bubbles

    Returns:
        True if successful, False otherwise
    """
    try:
        if not dialogue_lines or not any(line.strip() for line in dialogue_lines):
            # No dialogue to add, just copy the original
            import shutil
            shutil.copy2(panel_path, output_path)
            return True

        # Load image with OpenCV
        img = cv2.imread(panel_path)
        if img is None:
            print(f"❌ Could not load image: {panel_path}")
            return False

        h, w, _ = img.shape
        face = detect_face_bbox(img)

        # Decide bubble position
        if face:
            fx, fy, fw, fh = face
            # If face center is on right half, bubble on left; else on right
            bubble_x = int(w * 0.05) if (fx + fw / 2) > w / 2 else int(w * 0.5)
            bubble_y = int(h * 0.05)
        else:
            # Default top-left if no face found
            bubble_x, bubble_y = int(w * 0.05), int(h * 0.05)

        # Draw a white rectangle for the bubble
        text = "\n".join(dialogue_lines)
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.5
        thickness = 1

        # Estimate text size
        (text_w, text_h), _ = cv2.getTextSize(text, font, font_scale, thickness)
        pad = 10
        bubble_w = text_w + pad * 2
        bubble_h = text_h + pad * len(dialogue_lines)  # simple multi-line

        # Draw filled white rectangle for bubble
        cv2.rectangle(
            img,
            (bubble_x, bubble_y),
            (bubble_x + bubble_w, bubble_y + bubble_h),
            (255, 255, 255), -1  # filled white
        )

        # Draw black border around bubble
        cv2.rectangle(
            img,
            (bubble_x, bubble_y),
            (bubble_x + bubble_w, bubble_y + bubble_h),
            (0, 0, 0), 2  # black border
        )

        # Draw text
        cv2.putText(
            img,
            text,
            (bubble_x + pad, bubble_y + pad + int(text_h / 2)),
            font,
            font_scale,
            (0, 0, 0),
            thickness
        )

        # Save the result
        cv2.imwrite(output_path, img)
        print(f"✅ Added dialogue to panel: {output_path}")
        return True

    except Exception as e:
        print(f"❌ Error placing dialogue on {panel_path}: {e}")
        # Copy original as fallback
        try:
            import shutil
            shutil.copy2(panel_path, output_path)
        except:
            pass
        return False


def main():
    """Test the dialogue placement functionality."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Add dialogue bubbles to manga panels")
    parser.add_argument("panel_path", help="Path to the panel image")
    parser.add_argument("--dialogue", nargs="+", help="Dialogue lines to add")
    parser.add_argument("--output", help="Output path (default: panel_with_dialogue.png)")
    
    args = parser.parse_args()
    
    output_path = args.output or "panel_with_dialogue.png"
    dialogue = args.dialogue or ["Hello!", "How are you?"]
    
    success = place_dialogue(args.panel_path, dialogue, output_path)
    
    if success:
        print(f"✅ Dialogue added successfully: {output_path}")
        return 0
    else:
        print(f"❌ Failed to add dialogue")
        return 1


if __name__ == "__main__":
    exit(main())
