#!/usr/bin/env python3
"""
Dialogue Bubble Overlay Script

Places dialogue bubbles on manga panels with automatic positioning based on character faces.
"""

import cv2
import numpy as np
from pathlib import Path
from typing import List, Tuple, Dict, Any
from PIL import Image, ImageDraw, ImageFont
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from pipeline.utils import detect_faces


def find_face_position(image_path: str) -> Tuple[int, int, int, int]:
    """
    Find the largest face in the image and return its bounding box.
    
    Args:
        image_path: Path to the image file
        
    Returns:
        Tuple of (x, y, width, height) for the largest face, or (0, 0, 0, 0) if no face found
    """
    try:
        # Load the image
        image = cv2.imread(image_path)
        if image is None:
            return (0, 0, 0, 0)
        
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Load face cascade classifier
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        
        # Detect faces
        faces = face_cascade.detectMultiScale(gray, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30))
        
        if len(faces) == 0:
            return (0, 0, 0, 0)
        
        # Return the largest face
        largest_face = max(faces, key=lambda face: face[2] * face[3])
        return tuple(largest_face)
        
    except Exception as e:
        print(f"Error finding face position: {e}")
        return (0, 0, 0, 0)


def calculate_bubble_position(
    image_width: int, 
    image_height: int, 
    face_x: int, 
    face_y: int, 
    face_w: int, 
    face_h: int,
    bubble_width: int,
    bubble_height: int
) -> Tuple[int, int]:
    """
    Calculate optimal position for dialogue bubble based on face position.
    
    Args:
        image_width: Width of the image
        image_height: Height of the image
        face_x, face_y, face_w, face_h: Face bounding box
        bubble_width, bubble_height: Desired bubble dimensions
        
    Returns:
        Tuple of (bubble_x, bubble_y) for top-left corner of bubble
    """
    face_center_x = face_x + face_w // 2
    face_center_y = face_y + face_h // 2
    
    # Default position: top-left of image
    bubble_x = 20
    bubble_y = 20
    
    # If face is in right half, place bubble on left
    if face_center_x > image_width // 2:
        bubble_x = 20
    else:
        # Face is in left half, place bubble on right
        bubble_x = image_width - bubble_width - 20
    
    # If face is in bottom half, place bubble in top
    if face_center_y > image_height // 2:
        bubble_y = 20
    else:
        # Face is in top half, place bubble in bottom
        bubble_y = image_height - bubble_height - 20
    
    # Ensure bubble stays within image bounds
    bubble_x = max(10, min(bubble_x, image_width - bubble_width - 10))
    bubble_y = max(10, min(bubble_y, image_height - bubble_height - 10))
    
    return (bubble_x, bubble_y)


def create_speech_bubble(
    width: int, 
    height: int, 
    text_lines: List[str],
    font_size: int = 16
) -> Image.Image:
    """
    Create a speech bubble with text.
    
    Args:
        width: Bubble width
        height: Bubble height
        text_lines: List of text lines to display
        font_size: Font size for text
        
    Returns:
        PIL Image of the speech bubble
    """
    # Create bubble image with transparency
    bubble = Image.new('RGBA', (width, height), (255, 255, 255, 0))
    draw = ImageDraw.Draw(bubble)
    
    # Draw rounded rectangle for bubble
    corner_radius = 15
    draw.rounded_rectangle(
        [0, 0, width-1, height-1], 
        radius=corner_radius, 
        fill=(255, 255, 255, 240),  # White with slight transparency
        outline=(0, 0, 0, 255),     # Black border
        width=2
    )
    
    # Load font
    try:
        # Try to load a manga-style font if available
        font_path = Path("assets/fonts/manga_font.ttf")
        if font_path.exists():
            font = ImageFont.truetype(str(font_path), font_size)
        else:
            # Fallback to system font
            font = ImageFont.truetype("arial.ttf", font_size)
    except:
        # Ultimate fallback
        font = ImageFont.load_default()
    
    # Calculate text positioning
    total_text_height = len(text_lines) * (font_size + 4)
    start_y = (height - total_text_height) // 2
    
    # Draw each line of text
    for i, line in enumerate(text_lines):
        # Calculate text width for centering
        bbox = draw.textbbox((0, 0), line, font=font)
        text_width = bbox[2] - bbox[0]
        text_x = (width - text_width) // 2
        text_y = start_y + i * (font_size + 4)
        
        # Draw text with slight shadow for readability
        draw.text((text_x + 1, text_y + 1), line, fill=(128, 128, 128, 255), font=font)  # Shadow
        draw.text((text_x, text_y), line, fill=(0, 0, 0, 255), font=font)  # Main text
    
    return bubble


def place_dialogue(panel_path: str, dialogue: List[str], output_path: str) -> bool:
    """
    Place dialogue bubbles on a manga panel.
    
    Args:
        panel_path: Path to the original panel image
        dialogue: List of dialogue lines
        output_path: Path to save the panel with dialogue bubbles
        
    Returns:
        True if successful, False otherwise
    """
    try:
        if not dialogue or not any(line.strip() for line in dialogue):
            # No dialogue to add, just copy the original
            import shutil
            shutil.copy2(panel_path, output_path)
            return True
        
        # Load the panel image
        panel = Image.open(panel_path)
        panel_width, panel_height = panel.size
        
        # Convert to RGBA for transparency support
        if panel.mode != 'RGBA':
            panel = panel.convert('RGBA')
        
        # Find face position for bubble placement
        face_x, face_y, face_w, face_h = find_face_position(panel_path)
        
        # Filter out empty dialogue lines
        dialogue_lines = [line.strip() for line in dialogue if line.strip()]
        
        if not dialogue_lines:
            # No actual dialogue, just copy original
            panel.save(output_path)
            return True
        
        # Calculate bubble dimensions based on text
        max_line_length = max(len(line) for line in dialogue_lines)
        bubble_width = min(max(200, max_line_length * 8), panel_width - 40)
        bubble_height = min(max(80, len(dialogue_lines) * 25 + 20), panel_height - 40)
        
        # Calculate bubble position
        if face_w > 0 and face_h > 0:  # Face detected
            bubble_x, bubble_y = calculate_bubble_position(
                panel_width, panel_height, 
                face_x, face_y, face_w, face_h,
                bubble_width, bubble_height
            )
        else:
            # No face detected, place in top-left corner
            bubble_x, bubble_y = 20, 20
        
        # Create speech bubble
        bubble = create_speech_bubble(bubble_width, bubble_height, dialogue_lines)
        
        # Paste bubble onto panel
        panel.paste(bubble, (bubble_x, bubble_y), bubble)
        
        # Save the result
        panel.save(output_path)
        print(f"✅ Added dialogue to panel: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error placing dialogue on {panel_path}: {e}")
        # Copy original as fallback
        try:
            import shutil
            shutil.copy2(panel_path, output_path)
        except:
            pass
        return False


def main():
    """Test the dialogue placement functionality."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Add dialogue bubbles to manga panels")
    parser.add_argument("panel_path", help="Path to the panel image")
    parser.add_argument("--dialogue", nargs="+", help="Dialogue lines to add")
    parser.add_argument("--output", help="Output path (default: panel_with_dialogue.png)")
    
    args = parser.parse_args()
    
    output_path = args.output or "panel_with_dialogue.png"
    dialogue = args.dialogue or ["Hello!", "How are you?"]
    
    success = place_dialogue(args.panel_path, dialogue, output_path)
    
    if success:
        print(f"✅ Dialogue added successfully: {output_path}")
        return 0
    else:
        print(f"❌ Failed to add dialogue")
        return 1


if __name__ == "__main__":
    exit(main())
