"""
Pipeline Module

Main manga generation pipeline and utilities.
"""

from .generate_manga import MangaGenerator
from .utils import (
    setup_logging,
    set_seed,
    generate_hash,
    save_json,
    load_json,
    ensure_directory,
    clean_filename,
    timestamp_string,
    ProgressTracker,
    clean_visual_prompt,
    detect_faces,
    detect_blur,
    detect_pose_keypoints
)
from .prompt_builder import build_image_prompts, create_panel_sequence_prompts, enhance_prompt_for_style

__all__ = [
    "MangaGenerator",
    "setup_logging",
    "set_seed",
    "generate_hash",
    "save_json",
    "load_json",
    "ensure_directory",
    "clean_filename",
    "timestamp_string",
    "ProgressTracker",
    "clean_visual_prompt",
    "detect_faces",
    "detect_blur",
    "detect_pose_keypoints",
    "build_image_prompts",
    "create_panel_sequence_prompts",
    "enhance_prompt_for_style"
]
