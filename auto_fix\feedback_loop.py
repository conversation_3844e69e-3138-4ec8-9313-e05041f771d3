"""
Feedback Loop System for Manga Auto-Fix

Compares validation results before and after fixes to measure improvement.
"""

import json
from pathlib import Path
from typing import Dict, List, Any, Tuple
from datetime import datetime
from dataclasses import dataclass

from validation.vlm_validator import VLMValida<PERSON>, ValidationResult
from validation.report_generator import ValidationReportGenerator


@dataclass
class ImprovementMetrics:
    """Metrics comparing before and after validation results."""
    panel_path: str
    original_score: float
    fixed_score: float
    score_improvement: float
    original_issues: List[str]
    fixed_issues: List[str]
    issues_resolved: List[str]
    issues_remaining: List[str]
    new_issues: List[str]
    fix_successful: bool


class FeedbackLoop:
    """
    Feedback loop system for measuring and reporting fix effectiveness.
    """
    
    def __init__(self, 
                 primary_model: str = "qwen/qwen2.5-vl-7b-instruct:free",
                 fallback_keys: List[str] = None):
        """
        Initialize the feedback loop system.
        
        Args:
            primary_model: Primary VLM model for validation
            fallback_keys: List of fallback API keys
        """
        self.primary_model = primary_model
        self.fallback_keys = fallback_keys or [
            "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",
            "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"
        ]
        
    def run_feedback_loop(self, 
                         original_manga_dir: str, 
                         fixed_manga_dir: str,
                         original_validation_path: str = None) -> str:
        """
        Run complete feedback loop: validate fixed manga and compare results.
        
        Args:
            original_manga_dir: Path to original manga directory
            fixed_manga_dir: Path to fixed manga directory
            original_validation_path: Path to original validation results (optional)
            
        Returns:
            Path to improvement report
        """
        print(f"🔄 Starting feedback loop...")
        print(f"📁 Original: {original_manga_dir}")
        print(f"📁 Fixed: {fixed_manga_dir}")
        
        # Load original validation results
        if original_validation_path:
            original_results = self._load_validation_results(original_validation_path)
        else:
            # Look for validation results in original manga directory
            original_validation_path = Path(original_manga_dir) / "validation" / "validation_results.json"
            if original_validation_path.exists():
                original_results = self._load_validation_results(str(original_validation_path))
            else:
                print("⚠️  No original validation results found, running validation on original...")
                original_results = self._validate_manga_directory(original_manga_dir)
        
        # Validate fixed manga
        print(f"\n🔍 Validating fixed manga...")
        fixed_results = self._validate_manga_directory(fixed_manga_dir)
        
        # Compare results
        print(f"\n📊 Comparing results...")
        improvement_metrics = self._compare_results(original_results, fixed_results)
        
        # Generate improvement report
        print(f"\n📄 Generating improvement report...")
        report_path = self._generate_improvement_report(
            improvement_metrics, 
            original_manga_dir, 
            fixed_manga_dir
        )
        
        print(f"✅ Feedback loop complete!")
        print(f"📄 Improvement report: {report_path}")
        
        return report_path
    
    def _load_validation_results(self, results_path: str) -> List[ValidationResult]:
        """
        Load validation results from JSON file.
        
        Args:
            results_path: Path to validation results JSON
            
        Returns:
            List of ValidationResult objects
        """
        with open(results_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        results = []
        for result_data in data["results"]:
            result = ValidationResult(
                panel_path=result_data["panel_path"],
                scene_description=result_data["scene_description"],
                scene_aligned=result_data["scene_aligned"],
                scene_confidence=result_data["scene_confidence"],
                scene_reasoning=result_data["scene_reasoning"],
                emotion_target=result_data["emotion_target"],
                emotion_detected=result_data["emotion_detected"],
                emotion_aligned=result_data["emotion_aligned"],
                emotion_confidence=result_data["emotion_confidence"],
                emotion_reasoning=result_data["emotion_reasoning"],
                dialogue_placement_ok=result_data["dialogue_placement_ok"],
                dialogue_confidence=result_data["dialogue_confidence"],
                dialogue_reasoning=result_data["dialogue_reasoning"],
                overall_score=result_data["overall_score"],
                issues=result_data["issues"],
                timestamp=result_data["timestamp"]
            )
            results.append(result)
        
        return results
    
    def _validate_manga_directory(self, manga_dir: str) -> List[ValidationResult]:
        """
        Validate a manga directory and return results.
        
        Args:
            manga_dir: Path to manga directory
            
        Returns:
            List of validation results
        """
        from scripts.validate_manga import load_manga_data
        
        # Load panel data
        panels_data = load_manga_data(manga_dir)
        
        # Initialize validator with fallback support
        validator = VLMValidator(
            primary_model=self.primary_model,
            fallback_key=self.fallback_keys[0] if self.fallback_keys else None
        )
        
        # Validate panels
        results = validator.validate_manga_batch(panels_data)
        
        # Save validation results
        validation_dir = Path(manga_dir) / "validation"
        validation_dir.mkdir(exist_ok=True)
        
        report_generator = ValidationReportGenerator()
        report_generator.save_json_results(
            results, 
            str(validation_dir / "validation_results.json")
        )
        
        return results
    
    def _compare_results(self, 
                        original_results: List[ValidationResult], 
                        fixed_results: List[ValidationResult]) -> List[ImprovementMetrics]:
        """
        Compare original and fixed validation results.
        
        Args:
            original_results: Original validation results
            fixed_results: Fixed validation results
            
        Returns:
            List of improvement metrics
        """
        improvement_metrics = []
        
        # Create lookup for fixed results by panel name
        fixed_lookup = {}
        for result in fixed_results:
            panel_name = Path(result.panel_path).name
            fixed_lookup[panel_name] = result
        
        # Compare each original result with its fixed counterpart
        for original in original_results:
            original_panel_name = Path(original.panel_path).name
            
            # Find corresponding fixed result
            fixed = fixed_lookup.get(original_panel_name)
            
            if fixed:
                # Calculate improvements
                score_improvement = fixed.overall_score - original.overall_score
                
                # Analyze issue changes
                original_issues = set(original.issues)
                fixed_issues = set(fixed.issues)
                
                issues_resolved = list(original_issues - fixed_issues)
                issues_remaining = list(original_issues & fixed_issues)
                new_issues = list(fixed_issues - original_issues)
                
                fix_successful = (
                    score_improvement > 0 or 
                    len(issues_resolved) > 0 or
                    (len(new_issues) == 0 and fixed.overall_score >= 0.85)
                )
                
                metrics = ImprovementMetrics(
                    panel_path=original.panel_path,
                    original_score=original.overall_score,
                    fixed_score=fixed.overall_score,
                    score_improvement=score_improvement,
                    original_issues=original.issues,
                    fixed_issues=fixed.issues,
                    issues_resolved=issues_resolved,
                    issues_remaining=issues_remaining,
                    new_issues=new_issues,
                    fix_successful=fix_successful
                )
                
                improvement_metrics.append(metrics)
            else:
                print(f"⚠️  No fixed result found for panel: {original_panel_name}")
        
        return improvement_metrics
    
    def _generate_improvement_report(self, 
                                   metrics: List[ImprovementMetrics],
                                   original_dir: str,
                                   fixed_dir: str) -> str:
        """
        Generate comprehensive improvement report.
        
        Args:
            metrics: List of improvement metrics
            original_dir: Original manga directory
            fixed_dir: Fixed manga directory
            
        Returns:
            Path to improvement report
        """
        # Calculate summary statistics
        total_panels = len(metrics)
        successful_fixes = sum(1 for m in metrics if m.fix_successful)
        avg_score_improvement = sum(m.score_improvement for m in metrics) / total_panels if total_panels > 0 else 0
        
        total_issues_resolved = sum(len(m.issues_resolved) for m in metrics)
        total_new_issues = sum(len(m.new_issues) for m in metrics)
        
        # Generate report content
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        report_content = f"""# Manga Auto-Fix Improvement Report

**Generated:** {timestamp}

## 📊 Summary Statistics

- **Total Panels Processed:** {total_panels}
- **Successful Fixes:** {successful_fixes}/{total_panels} ({successful_fixes/total_panels*100:.1f}%)
- **Average Score Improvement:** {avg_score_improvement:+.3f}
- **Total Issues Resolved:** {total_issues_resolved}
- **New Issues Introduced:** {total_new_issues}

## 🎯 Overall Assessment

"""
        
        if successful_fixes / total_panels >= 0.8:
            report_content += "✅ **Excellent improvement** - Most panels were successfully fixed.\n\n"
        elif successful_fixes / total_panels >= 0.6:
            report_content += "🟡 **Good improvement** - Majority of panels were improved.\n\n"
        elif successful_fixes / total_panels >= 0.4:
            report_content += "🟠 **Moderate improvement** - Some panels were improved.\n\n"
        else:
            report_content += "❌ **Limited improvement** - Few panels were successfully fixed.\n\n"
        
        # Panel-by-panel results
        report_content += "## 📋 Panel-by-Panel Improvements\n\n"
        
        for i, metric in enumerate(metrics, 1):
            panel_name = Path(metric.panel_path).name
            status = "✅" if metric.fix_successful else "❌"
            
            report_content += f"""### Panel {i}: {panel_name} {status}

**Score Change:** {metric.original_score:.2f} → {metric.fixed_score:.2f} ({metric.score_improvement:+.3f})

| Aspect | Before | After | Status |
|--------|--------|-------|--------|
| Overall Score | {metric.original_score:.2f} | {metric.fixed_score:.2f} | {"✅ Improved" if metric.score_improvement > 0 else "❌ Declined" if metric.score_improvement < 0 else "➖ No Change"} |
| Issues | {len(metric.original_issues)} | {len(metric.fixed_issues)} | {"✅ Reduced" if len(metric.fixed_issues) < len(metric.original_issues) else "❌ Increased" if len(metric.fixed_issues) > len(metric.original_issues) else "➖ Same"} |

**Issues Resolved:** {', '.join(metric.issues_resolved) if metric.issues_resolved else 'None'}

**Issues Remaining:** {', '.join(metric.issues_remaining) if metric.issues_remaining else 'None'}

**New Issues:** {', '.join(metric.new_issues) if metric.new_issues else 'None'}

---

"""
        
        # Save report
        report_dir = Path(fixed_dir) / "improvement_analysis"
        report_dir.mkdir(exist_ok=True)
        
        report_path = report_dir / "improvement_report.md"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        # Also save metrics as JSON
        json_path = report_dir / "improvement_metrics.json"
        self._save_metrics_json(metrics, json_path, original_dir, fixed_dir)
        
        return str(report_path)
    
    def _save_metrics_json(self, 
                          metrics: List[ImprovementMetrics],
                          json_path: Path,
                          original_dir: str,
                          fixed_dir: str):
        """
        Save improvement metrics as JSON.
        
        Args:
            metrics: List of improvement metrics
            json_path: Path to save JSON file
            original_dir: Original manga directory
            fixed_dir: Fixed manga directory
        """
        json_data = {
            "timestamp": datetime.now().isoformat(),
            "original_directory": original_dir,
            "fixed_directory": fixed_dir,
            "summary": {
                "total_panels": len(metrics),
                "successful_fixes": sum(1 for m in metrics if m.fix_successful),
                "average_score_improvement": sum(m.score_improvement for m in metrics) / len(metrics) if metrics else 0,
                "total_issues_resolved": sum(len(m.issues_resolved) for m in metrics),
                "total_new_issues": sum(len(m.new_issues) for m in metrics)
            },
            "panel_metrics": []
        }
        
        for metric in metrics:
            json_data["panel_metrics"].append({
                "panel_path": metric.panel_path,
                "original_score": metric.original_score,
                "fixed_score": metric.fixed_score,
                "score_improvement": metric.score_improvement,
                "original_issues": metric.original_issues,
                "fixed_issues": metric.fixed_issues,
                "issues_resolved": metric.issues_resolved,
                "issues_remaining": metric.issues_remaining,
                "new_issues": metric.new_issues,
                "fix_successful": metric.fix_successful
            })
        
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2)
        
        print(f"💾 Improvement metrics saved: {json_path}")
