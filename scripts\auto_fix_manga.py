#!/usr/bin/env python3
"""
Auto-Fix Manga Script

Automatically fixes issues identified by VLM validation and measures improvement.
"""

import os
import sys
import argparse
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from auto_fix.issue_analyzer import IssueAnalyzer
from auto_fix.fix_engine import AutoFixEngine
from auto_fix.feedback_loop import Fe<PERSON><PERSON><PERSON><PERSON>


def auto_fix_manga(manga_dir: str, 
                  validation_results_path: str = None,
                  confidence_threshold: float = 0.75) -> str:
    """
    Automatically fix manga issues and measure improvement.
    
    Args:
        manga_dir: Path to manga directory
        validation_results_path: Path to validation results JSON (optional)
        confidence_threshold: Minimum acceptable confidence score
        
    Returns:
        Path to fixed manga directory
    """
    manga_path = Path(manga_dir)
    
    if not manga_path.exists():
        raise FileNotFoundError(f"Manga directory not found: {manga_dir}")
    
    print(f"🔧 Starting auto-fix process for: {manga_path.name}")
    print(f"📊 Confidence threshold: {confidence_threshold}")
    
    # Step 1: Load and analyze validation results
    print(f"\n📋 Step 1: Analyzing validation results...")
    
    if validation_results_path is None:
        # Look for validation results in manga directory
        validation_results_path = manga_path / "validation" / "validation_results.json"
        
        if not validation_results_path.exists():
            print(f"❌ No validation results found. Please run validation first:")
            print(f"   python scripts/validate_manga.py {manga_dir}")
            return None
    
    analyzer = IssueAnalyzer(confidence_threshold=confidence_threshold)
    validation_results = analyzer.load_validation_results(str(validation_results_path))
    
    print(f"📊 Loaded {len(validation_results)} validation results")
    
    # Identify fixable issues
    fix_strategies = analyzer.identify_fixable_issues(validation_results)
    
    if not fix_strategies:
        print(f"✅ No fixable issues found! All panels meet quality standards.")
        return str(manga_path)
    
    print(f"🎯 Identified {len(fix_strategies)} fixable issues:")
    
    # Group strategies by type
    strategy_counts = {}
    for strategy in fix_strategies:
        strategy_counts[strategy.issue_type] = strategy_counts.get(strategy.issue_type, 0) + 1
    
    for issue_type, count in strategy_counts.items():
        print(f"   - {issue_type}: {count} panels")
    
    # Step 2: Apply fixes
    print(f"\n🔧 Step 2: Applying fixes...")
    
    fix_engine = AutoFixEngine()
    fixed_manga_dir = fix_engine.apply_fixes(fix_strategies, str(manga_path))
    
    # Step 3: Run feedback loop
    print(f"\n🔄 Step 3: Running feedback loop...")
    
    feedback_loop = FeedbackLoop()
    improvement_report = feedback_loop.run_feedback_loop(
        original_manga_dir=str(manga_path),
        fixed_manga_dir=fixed_manga_dir,
        original_validation_path=str(validation_results_path)
    )
    
    print(f"\n✅ Auto-fix process complete!")
    print(f"📁 Fixed manga: {fixed_manga_dir}")
    print(f"📄 Improvement report: {improvement_report}")
    
    return fixed_manga_dir


def main():
    """Main entry point for the auto-fix script."""
    parser = argparse.ArgumentParser(
        description="Automatically fix manga panel issues using VLM feedback",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Auto-fix manga with existing validation results
  python scripts/auto_fix_manga.py outputs/manga_20250601_173550
  
  # Auto-fix with custom validation results
  python scripts/auto_fix_manga.py outputs/manga_20250601_173550 --validation validation_results.json
  
  # Auto-fix with custom confidence threshold
  python scripts/auto_fix_manga.py outputs/manga_20250601_173550 --threshold 0.8
        """
    )
    
    parser.add_argument(
        "manga_dir",
        help="Path to manga directory containing panels and validation results"
    )
    parser.add_argument(
        "--validation", "-v",
        help="Path to validation results JSON file (default: manga_dir/validation/validation_results.json)"
    )
    parser.add_argument(
        "--threshold", "-t",
        type=float,
        default=0.75,
        help="Confidence threshold for fixes (default: 0.75)"
    )
    parser.add_argument(
        "--dry-run", "-d",
        action="store_true",
        help="Analyze issues without applying fixes"
    )
    
    args = parser.parse_args()
    
    try:
        if args.dry_run:
            # Dry run - just analyze issues
            print(f"🔍 Dry run mode: Analyzing issues without applying fixes...")
            
            manga_path = Path(args.manga_dir)
            validation_path = args.validation or (manga_path / "validation" / "validation_results.json")
            
            if not Path(validation_path).exists():
                print(f"❌ Validation results not found: {validation_path}")
                print(f"   Run validation first: python scripts/validate_manga.py {args.manga_dir}")
                sys.exit(1)
            
            analyzer = IssueAnalyzer(confidence_threshold=args.threshold)
            validation_results = analyzer.load_validation_results(str(validation_path))
            fix_strategies = analyzer.identify_fixable_issues(validation_results)
            
            print(f"\n📊 Analysis Results:")
            print(f"   Total panels: {len(validation_results)}")
            print(f"   Panels needing fixes: {len(fix_strategies)}")
            
            if fix_strategies:
                strategy_counts = {}
                for strategy in fix_strategies:
                    strategy_counts[strategy.issue_type] = strategy_counts.get(strategy.issue_type, 0) + 1
                
                print(f"\n🎯 Issues by type:")
                for issue_type, count in strategy_counts.items():
                    print(f"   - {issue_type}: {count} panels")
                
                print(f"\n🔧 Fix methods:")
                method_counts = {}
                for strategy in fix_strategies:
                    method_counts[strategy.fix_method] = method_counts.get(strategy.fix_method, 0) + 1
                
                for method, count in method_counts.items():
                    print(f"   - {method}: {count} panels")
            else:
                print(f"✅ No issues found that need fixing!")
        
        else:
            # Full auto-fix process
            fixed_dir = auto_fix_manga(
                manga_dir=args.manga_dir,
                validation_results_path=args.validation,
                confidence_threshold=args.threshold
            )
            
            if fixed_dir:
                print(f"\n🎉 Auto-fix completed successfully!")
                print(f"📁 Fixed manga directory: {fixed_dir}")
                print(f"📄 Check the improvement report for detailed analysis.")
            else:
                print(f"❌ Auto-fix failed or no fixes needed.")
                sys.exit(1)
        
    except Exception as e:
        print(f"❌ Auto-fix failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
