#!/usr/bin/env python3
"""
Manga Validation Script

Validates manga panels using VLM (Vision-Language Model) analysis.
Checks scene alignment, emotion consistency, and dialogue placement.
"""

import os
import sys
import json
import argparse
from pathlib import Path
from typing import List, Dict, Any

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from validation.vlm_validator import VLMValidator
from validation.report_generator import ValidationReportGenerator


def load_manga_data(manga_dir: str) -> List[Dict[str, Any]]:
    """
    Load manga panel data from a manga output directory.
    
    Args:
        manga_dir: Path to manga output directory
        
    Returns:
        List of panel data dictionaries
    """
    manga_path = Path(manga_dir)
    
    # Try to load story structure for scene descriptions
    story_file = manga_path / "story_structure.json"
    manga_results_file = manga_path / "manga_results.json"
    
    panels_data = []
    
    # Load story structure if available
    story_data = {}
    if story_file.exists():
        try:
            with open(story_file, 'r', encoding='utf-8') as f:
                story_data = json.load(f)
        except Exception as e:
            print(f"⚠️  Could not load story structure: {e}")
    
    # Load manga results if available
    manga_results = {}
    if manga_results_file.exists():
        try:
            with open(manga_results_file, 'r', encoding='utf-8') as f:
                manga_results = json.load(f)
        except Exception as e:
            print(f"⚠️  Could not load manga results: {e}")
    
    # Find all panel images
    panel_files = []
    
    # Look for panels with dialogue bubbles first
    for chapter_dir in manga_path.glob("chapter_*"):
        bubble_dir = chapter_dir / "with_bubbles"
        if bubble_dir.exists():
            # Prefer bubble versions
            bubble_files = list(bubble_dir.glob("*_bubble.png"))
            if bubble_files:
                panel_files.extend(bubble_files)
                continue
        
        # Fall back to regular panels
        scene_files = list(chapter_dir.glob("scene_*.png"))
        panel_files.extend(scene_files)
    
    # Sort panels by name for consistent ordering
    panel_files.sort(key=lambda x: x.name)
    
    print(f"📁 Found {len(panel_files)} panels in {manga_dir}")
    
    # Create panel data entries
    for i, panel_path in enumerate(panel_files):
        # Extract chapter and scene info from path
        parts = panel_path.parts
        chapter_name = None
        scene_name = None
        
        for part in parts:
            if part.startswith("chapter_"):
                chapter_name = part
            elif part.startswith("scene_"):
                scene_name = part.replace(".png", "").replace("_bubble", "")
        
        # Try to get scene description from story data
        scene_description = f"Scene from {chapter_name or 'unknown chapter'}"
        target_emotion = None
        
        if story_data and "chapters" in story_data:
            try:
                # Find matching chapter and scene
                for chapter in story_data["chapters"]:
                    if chapter.get("title", "").lower().replace(" ", "_") in (chapter_name or ""):
                        if "scenes" in chapter:
                            scene_index = int(scene_name.split("_")[-1]) - 1 if scene_name else i % len(chapter["scenes"])
                            if 0 <= scene_index < len(chapter["scenes"]):
                                scene_info = chapter["scenes"][scene_index]
                                scene_description = scene_info.get("description", scene_description)
                                target_emotion = scene_info.get("emotion")
                                break
            except Exception as e:
                print(f"⚠️  Error extracting scene info for {panel_path.name}: {e}")
        
        panels_data.append({
            "image_path": str(panel_path),
            "scene_description": scene_description,
            "emotion": target_emotion,
            "chapter": chapter_name,
            "scene": scene_name
        })
    
    return panels_data


def validate_manga_directory(manga_dir: str, output_dir: str = None) -> str:
    """
    Validate all panels in a manga directory and generate reports.
    
    Args:
        manga_dir: Path to manga output directory
        output_dir: Output directory for validation reports
        
    Returns:
        Path to validation output directory
    """
    manga_path = Path(manga_dir)
    
    if not manga_path.exists():
        raise FileNotFoundError(f"Manga directory not found: {manga_dir}")
    
    # Setup output directory
    if output_dir is None:
        output_dir = manga_path / "validation"
    else:
        output_dir = Path(output_dir)
    
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"🎯 Starting manga validation for: {manga_path.name}")
    print(f"📁 Output directory: {output_dir}")
    
    # Load panel data
    panels_data = load_manga_data(manga_dir)
    
    if not panels_data:
        raise ValueError(f"No panels found in {manga_dir}")
    
    # Initialize validator
    validator = VLMValidator()
    
    # Validate panels
    print(f"\n🔍 Validating {len(panels_data)} panels...")
    results = validator.validate_manga_batch(panels_data)
    
    # Generate reports
    print(f"\n📊 Generating validation reports...")
    report_generator = ValidationReportGenerator()
    
    manga_title = f"Validation Report - {manga_path.name}"
    
    # Save JSON results
    json_path = output_dir / "validation_results.json"
    report_generator.save_json_results(results, str(json_path))
    
    # Generate Markdown report
    md_path = output_dir / "validation_report.md"
    report_generator.generate_markdown_report(results, str(md_path), manga_title)
    
    # Generate HTML report
    html_path = output_dir / "validation_report.html"
    report_generator.generate_html_report(results, str(html_path), manga_title)
    
    # Print summary
    total_panels = len(results)
    issues_count = sum(1 for r in results if r.issues)
    avg_score = sum(r.overall_score for r in results) / total_panels if total_panels > 0 else 0
    
    print(f"\n✅ Validation complete!")
    print(f"📊 Summary:")
    print(f"   Total panels: {total_panels}")
    print(f"   Panels with issues: {issues_count}")
    print(f"   Average score: {avg_score:.2f}/1.0")
    print(f"📁 Reports saved to: {output_dir}")
    
    return str(output_dir)


def main():
    """Main entry point for the validation script."""
    parser = argparse.ArgumentParser(
        description="Validate manga panels using VLM analysis",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python scripts/validate_manga.py outputs/manga_20250601_173550
  python scripts/validate_manga.py outputs/manga_20250601_173550 --output validation_results
  python scripts/validate_manga.py outputs/manga_20250601_173550 --model qwen/qwen2.5-vl-72b-instruct:free
        """
    )
    
    parser.add_argument(
        "manga_dir",
        help="Path to manga output directory containing panels"
    )
    parser.add_argument(
        "--output", "-o",
        help="Output directory for validation reports (default: manga_dir/validation)"
    )
    parser.add_argument(
        "--model", "-m",
        default="qwen/qwen2.5-vl-72b-instruct:free",
        help="VLM model to use for validation (default: qwen/qwen2.5-vl-72b-instruct:free)"
    )
    
    args = parser.parse_args()
    
    try:
        # Validate the manga
        output_dir = validate_manga_directory(args.manga_dir, args.output)
        
        print(f"\n🎉 Validation completed successfully!")
        print(f"📄 View reports:")
        print(f"   Markdown: {Path(output_dir) / 'validation_report.md'}")
        print(f"   HTML: {Path(output_dir) / 'validation_report.html'}")
        print(f"   JSON: {Path(output_dir) / 'validation_results.json'}")
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
